<script setup>
import { ref, defineProps, defineEmits, reactive, watch, onMounted } from 'vue'
import { selectCell, AREATYPE_TEXT, CELLTYPE_TEXT, NULLCELL_TEXT } from '@/api/outbound/intercept'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  whCode: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:visible', 'checkCell', 'pagination-change'])

// 库位列表
const cellList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 查询条件
const queryParams = reactive({
  areaType: '', //库区类型
  areaNo: '', //库区编码
  cellType: '', //库位类型
  isNullCell: '', //是否为空
  cellNo: '', //库位编码
})

//库区类型
const areaTypeOptions = [
  { value: 1, label: '拣选区' },
  { value: 2, label: '存储区' },
  { value: 3, label: '残次品区' },
  { value: 4, label: '暂存区' },
  { value: 5, label: '其他区' },
]

//库位类型
const cellTypeOptions = [
  { value: 1, label: '货架' },
  { value: 2, label: '地推' },
]

//库位是否为空
const isNullCellOptions = [
  { value: 0, label: '否' },
  { value: 1, label: '是' },
]

function searchCell() {
  fetchCellList()
}

const fetchCellList = async () => {
  try {
    // 构建查询参数
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      productQuality: 0,
      whCode: props.whCode,
    }

    // 根据搜索类型和值拼接参数
    if (queryParams.areaType) {
      params['areaType'] = queryParams.areaType
    }
    if (queryParams.areaNo) {
      params['areaNo'] = queryParams.areaNo
    }
    if (queryParams.cellType) {
      params['cellType'] = queryParams.cellType
    }
    if (queryParams.cellNo) {
      params['cellNo'] = queryParams.cellNo
    }
    if (queryParams.isNullCell != '') {
      params['isNullCell'] = queryParams.isNullCell
    }

    const response = await selectCell(params)

    if (response && response.code == 200) {
      cellList.value = response.data.records || []
      total.value = response.data.total || 0
      currentPage.value = response.data.current || 1
      pageSize.value = response.data.size || 20
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  }
}

// 处理页码或每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchCellList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchCellList()
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      fetchCellList()
    }
  },
)

const checkCell = async (row) => {
  try {
    emit('checkCell', row)
  } catch (error) {
    console.error('操作失败', error)
  }
}

// 初始化
onMounted(() => {
  if (props.visible) {
    fetchCellList()
  }
})
</script>

<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    width="800px"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-divider />
    <el-form :model="queryParams" inline>
      <el-form-item prop="areaType" class="search-item">
        <el-select
          v-model="queryParams.areaType"
          clearable
          placeholder="库区类型"
          style="width: 145px"
          @change="searchCell"
        >
          <el-option
            v-for="dict in areaTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="areaNo" class="search-item">
        <el-input
          placeholder="库区编码"
          v-model="queryParams.areaNo"
          style="width: 145px"
          clearable
          @keyup.enter="searchCell"
          @clear="searchCell"
        >
          <template #suffix>
            <el-icon @click="handelCellNo"><Search /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="cellType" class="search-item">
        <el-select
          v-model="queryParams.cellType"
          clearable
          placeholder="货架"
          style="width: 145px"
          @change="searchCell"
        >
          <el-option
            v-for="dict in cellTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="isNullCell" class="search-item">
        <el-select
          v-model="queryParams.isNullCell"
          clearable
          placeholder="是否为空库位"
          style="width: 145px"
          @change="searchCell"
        >
          <el-option
            v-for="dict in isNullCellOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="cellNo" class="search-item">
        <el-input
          placeholder="库位编码"
          v-model="queryParams.cellNo"
          style="width: 145px"
          @keyup.enter="searchCell"
          @clear="searchCell"
        >
          <template #suffix>
            <el-icon @click="handelCellNo"><Search /></el-icon>
          </template>
        </el-input>
      </el-form-item>
    </el-form>

    <el-table
      ref=""
      height="400px"
      v-loading="loading"
      border="true"
      :data="cellList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="库位编码" prop="cellNo" />
      <el-table-column label="库位类型" prop="">
        <template #default="scope">
          {{ CELLTYPE_TEXT[scope.row.cellType] }}
        </template>
      </el-table-column>
      <el-table-column label="库区编码" prop="areaNo" />
      <el-table-column label="库区类型" prop="">
        <template #default="scope">
          {{ AREATYPE_TEXT[scope.row.areaType] }}
        </template>
      </el-table-column>
      <el-table-column label="是否为空库位" prop="">
        <template #default="scope">
          {{ NULLCELL_TEXT[scope.row.isNullCell] }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-link type="primary" @click="checkCell(scope.row)">选择</el-link>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.search-item {
  margin-right: 8px;
}
</style>
