<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'
import { getWaveDetail, getWaveSkuList } from '@/api/outbound/wave'
import { useLayoutStore } from '@/store/modules/layout'

// 路由
const route = useRoute()
const router = useRouter()

// 布局store
const layoutStore = useLayoutStore()

// 波次号
const waveNo = ref(route.query.waveNo || '')

// 页面加载状态
const loading = ref(false)

// 波次基本信息 - 模拟数据
const waveDetail = ref({
  waveNo: 'W250729AXO',
  status: 20,
  statusName: '拣货中',
  orderCount: 1,
  planQty: 2,
  pickQty: 0,
  assigntor: '一号头',
  createTime: '2024-07-29 10:30:00',
  pickingTypeName: '一单多件'
})

// 产品列表 - 模拟数据
const productList = ref([
  {
    id: 1,
    productSku: 'demo2',
    productSkuName: 'demo2',
    productName: 'demo2',
    planQty: 1,
    pickQty: 0,
    remainQty: 1,
    stockQualityName: '正常',
    stockQuality: 0,
    pickingQty: 0,
    pickingLocation: '',
    stockCount: 56
  }
])

const selectedProducts = ref([])

// 库位设置
const locationSettings = ref([
  {
    id: 1,
    cellNo: 'TMP',
    areaType: 1,
    areaTypeName: '拣选区',
    cellType: 1,
    cellTypeName: '货架'
  }
])





// 获取波次详情
const fetchWaveDetail = async () => {
  if (!waveNo.value) {
    ElMessage.error('波次号不能为空')
    return
  }

  loading.value = true

  try {
    const params = {
      waveNo: waveNo.value,
      whCode: 'CA'
    }

    const response = await getWaveDetail(params)

    if (response && response.code === 200) {
      waveDetail.value = response.data || {}
    }
  } catch (error) {
    console.error('获取波次详情失败', error)
    ElMessage.error('获取波次详情失败')
  } finally {
    loading.value = false
  }
}

// 获取产品列表
const fetchProductList = async () => {
  if (!waveNo.value) return

  loading.value = true

  try {
    const params = {
      waveNo: waveNo.value,
      whCode: 'CA',
      current: 1,
      size: 1000 // 获取所有产品
    }

    const response = await getWaveSkuList(params)

    if (response && response.code === 200) {
      // 为每个产品添加拣货数量字段
      productList.value = (response.data.records || []).map(item => ({
        ...item,
        pickingQty: 0, // 拣货数量
        remainQty: item.planQty - item.pickQty, // 剩余数量
        selected: false
      }))
    }
  } catch (error) {
    console.error('获取产品列表失败', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

// 复制波次号
const copyWaveNo = async () => {
  try {
    await navigator.clipboard.writeText(waveDetail.value.waveNo)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败', error)
    ElMessage.error('复制失败')
  }
}

// 处理产品选择
const handleProductSelection = (selection) => {
  selectedProducts.value = selection
}

// 处理拣货数量变化
const handlePickingQtyChange = (row, value) => {
  if (value < 0) {
    row.pickingQty = 0
    ElMessage.warning('拣货数量不能小于0')
    return
  }
  
  if (value > row.remainQty) {
    row.pickingQty = row.remainQty
    ElMessage.warning('拣货数量不能大于剩余数量')
    return
  }
  
  row.pickingQty = value
}





// 确认拣货
const confirmPicking = async () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请选择要拣货的产品')
    return
  }

  const hasPickingQty = selectedProducts.value.some(item => item.pickingQty > 0)
  if (!hasPickingQty) {
    ElMessage.warning('请填写拣货数量')
    return
  }

  if (locationSettings.value.length === 0) {
    ElMessage.warning('请设置拣货库位')
    return
  }

  try {
    await ElMessageBox.confirm('确认提交拣货信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // TODO: 调用拣货API
    ElMessage.success('拣货信息提交成功')
    
    // 返回波次列表
    router.push('/outbound/wave')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交拣货信息失败', error)
      ElMessage.error('提交拣货信息失败')
    }
  }
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 页面初始化
onMounted(() => {
  if (waveNo.value) {
    // fetchWaveDetail()
    // fetchProductList()
  }
})

// 计算总拣货数量
const totalPickingQty = computed(() => {
  return selectedProducts.value.reduce((sum, item) => sum + (item.pickingQty || 0), 0)
})

// 计算侧边栏宽度
const sidebarWidth = computed(() => {
  if (!layoutStore.showSidebar) return 0
  return layoutStore.isCollapsed ? 60 : 200
})
</script>

<template>
  <div class="wave-picking-container" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="wave-title">
          {{ waveDetail.waveNo }}
          <el-button
            :icon="DocumentCopy"
            size="small"
            text
            @click="copyWaveNo"
            title="复制波次号"
          />
        </h2>
        <div class="wave-stats">
          <span>波次数量：{{ waveDetail.orderCount }}</span>
          <span>出库单数量：{{ waveDetail.orderCount }}</span>
          <span>小包出库单：{{ waveDetail.orderCount }}</span>
          <span>订单数量：{{ waveDetail.orderCount }}</span>
          <span>产品数量：{{ waveDetail.planQty }}</span>
        </div>
      </div>
    </div>





    <!-- 产品列表 -->
    <el-card class="product-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>产品列表</span>
          <div class="summary-info">
            <span>已选择：{{ selectedProducts.length }} 个产品</span>
            <span>拣货数量：{{ totalPickingQty }}</span>
          </div>
        </div>
      </template>

      <el-table
        :data="productList"
        @selection-change="handleProductSelection"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="productSku" label="SKU" width="120" />

        <el-table-column prop="productSkuName" label="产品名称" width="120" />

        <el-table-column prop="productName" label="产品名称" width="120" />

        <el-table-column label="已拣/应拣" width="120" align="center">
          <template #default="scope">
            <span>{{ scope.row.pickQty }}/{{ scope.row.planQty }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="stockQualityName" label="库存属性" width="100" align="center" />

        <el-table-column label="拣货库位" width="150" align="center">
          <template #default="scope">
            <el-select
              v-model="scope.row.pickingLocation"
              placeholder="选择库位"
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="location in locationSettings"
                :key="location.cellNo"
                :label="location.cellNo"
                :value="location.cellNo"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column prop="stockCount" label="库存产品数量" width="120" align="center">
          <template #default="scope">
            <span class="stock-count">{{ scope.row.stockCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="拣货数量" width="120" align="center">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.pickingQty"
              :min="0"
              :max="scope.row.remainQty"
              size="small"
              @change="(value) => handlePickingQtyChange(scope.row, value)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 底部操作按钮 - 固定在底部 -->
    <div class="footer-actions" :style="{ left: sidebarWidth + 'px' }">
      <el-button size="large" @click="goBack">取消</el-button>
      <el-button type="primary" size="large" @click="confirmPicking">确认</el-button>
    </div>
  </div>
</template>

<style scoped>
.wave-picking-container {
  padding: 20px 20px 100px 20px; /* 底部留出空间给固定按钮 */
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.wave-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.wave-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.product-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}



.text-danger {
  color: var(--el-color-danger);
}

.stock-count {
  color: var(--el-color-primary);
  font-weight: 500;
}

.footer-actions {
  position: fixed;
  bottom: 0;
  left: 200px; /* 默认侧边栏宽度 */
  right: 0;
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-lighter);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: left var(--layout-transition-duration, 0.3s);
}

/* 侧边栏折叠时的样式 */
.layout-sidebar .footer-actions {
  left: 200px; /* 展开状态 */
}

/* 如果需要支持折叠状态，可以通过JavaScript动态设置 */

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-card__header) {
  background-color: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-lighter);
}
</style>
