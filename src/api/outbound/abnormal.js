import request from '@/utils/request'

/**
 * 获取异常件列表
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function getAbnormalList(data) {
  return request({
    url: '/outbound/abnormal/listing',
    method: 'post',
    data,
  })
}

/**
 * 移除异常件
 * @param {Object} data
 * @returns {Promise}
 */
export function removeEx(data) {
  return request({
    url: '/outbound/abnormal/removeEx',
    method: 'post',
    data,
  })
}

/**
 * 确认异常件
 * @param {Object} data 
 * @returns {Promise}
 */
export function confirmEx(data) {
  return request({
    url: '/outbound/abnormal/confirmEx',
    method: 'post',
    data,
  })
}

/**
 * 批量处理异常件
 * @param {Object} data - 处理参数
 * @returns {Promise}
 */
export function batchProcessAbnormal(data) {
  // 暂时使用模拟数据，等待后端API
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('批量处理数据:', data)
      const mockData = {
        code: 200,
        message: '批量处理成功',
        data: null
      }
      resolve(mockData)
    }, 500)
  })
}

/**
 * 导出异常件数据
 * @param {Object} data - 导出参数
 * @returns {Promise}
 */
export function exportAbnormalData(data) {
  // 暂时使用模拟数据，等待后端API
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('导出数据:', data)
      const mockData = {
        code: 200,
        message: '导出成功',
        data: null
      }
      resolve(mockData)
    }, 1000)
  })
}