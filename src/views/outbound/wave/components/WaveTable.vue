<script setup>
import { useWaveConfig, useWaveStatus } from '@/composables/useWaveConfig'
import {
  MERGE_STATUS_TEXT,
  OUTBOUND_STATUS_TEXT,
  REVIEW_STATUS_TEXT,
  SORING_STATUS_TEXT,
} from '@/constants/wave'
import { MoreFilled, Warning } from '@element-plus/icons-vue'
import { defineEmits, defineProps, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  getPreviewAndDownLoadUrl,
  listReprintOrder,
  clickMergeLogistics,
} from '@/api/outbound/wave'
import { installLodop, getLodop } from '@/utils/lodop/lodop'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  waveList: {
    type: Array,
    required: true,
  },
  total: {
    type: Number,
    default: 0,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  tableColumns: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits([
  'selection-change',
  'pagination-change',
  'assign-picker',
  'start-picking',
  'finish-picking',
  'view-detail',
  'wave-download',
])

// 路由
const router = useRouter()

// 使用配置组合式函数
const { getVisibleRowActions } = useWaveConfig()
const { getStatusTagType, formatTime } = useWaveStatus()

// 打印相关状态
const lodop = ref(null)
const lodopError = ref('')
const lodopChecking = ref(true)
const printLoading = ref(false)
const printerList = ref([])
const selectedPrinter = ref('')

// 打印确认对话框状态
const printConfirmDialog = ref({
  visible: false,
  printData: [],
  currentRow: null,
})

// 打印机选择对话框状态
const printerSelectDialog = ref({
  visible: false,
  printData: [],
  currentRow: null,
})

// 表格选中行
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
  emit('selection-change', selection)
}

// 处理页码或每页条数变化
const handleSizeChange = (size) => {
  emit('pagination-change', { page: props.currentPage, size })
}

const handleCurrentChange = (page) => {
  emit('pagination-change', { page, size: props.pageSize })
}

// 按钮分组逻辑
const getGroupedActions = (row) => {
  const allActions = getVisibleRowActions(row)
  if (allActions.length <= 1) {
    return {
      primaryActions: allActions,
      moreActions: [],
    }
  }
  return {
    primaryActions: allActions.slice(0, 1),
    moreActions: allActions.slice(1),
  }
}

// 统一的行操作处理
const handleRowAction = (actionKey, row) => {
  const actionMap = {
    view: () => emit('view-detail', row), // 详情
    picking: () => emit('picking', row), // todo 拣货
    print_summary: () => emit('wave-download', row, 1), //打印汇总拣货单
    print_sorting: () => emit('wave-download', row, 2), //打印分拣拣货单
    second_sorting: () => handleSecondSorting(row), //二次分拣
    assign_picker: () => emit('assign_picker', row), // todo 分配拣货员
    waybill_merge: () => handleWaybillMerge(row), //面单拼接
    waybill_view: () => handleWaybillView(row), //查看面单
    waybill_print: () => handleWaybillPrint(row), //面单打印
  }

  const action = actionMap[actionKey]
  if (action) {
    action()
  } else {
    console.warn(`未知的操作: ${actionKey}`)
  }
}

// 面单查看
const handleWaybillView = async (row) => {
  try {
    const params = {
      customerCode: row.customerCode || '',
      fileKey: row.fileKey || '',
      fileName: row.fileName || '',
      whCode: row.whCode || 'CA',
    }

    const response = await getPreviewAndDownLoadUrl(params)

    if (response && response.code === 200 && response.data?.previewUrl) {
      // 在新窗口打开预览URL
      window.open(response.data.previewUrl, '_blank')
    } else {
      ElMessage.error(response?.msg || '查看面单失败')
    }
  } catch (error) {
    console.error('查看面单失败', error)
    ElMessage.error('查看面单失败')
  }
}

// 面单打印
const handleWaybillPrint = async (row) => {
  try {
    const params = {
      waveNo: row.waveNo,
      whCode: row.whCode || 'CA',
    }

    const response = await listReprintOrder(params)

    if (response && response.code === 200 && response.data) {
      printConfirmDialog.value.printData = response.data
      printConfirmDialog.value.currentRow = row
      printConfirmDialog.value.visible = true
    } else {
      ElMessage.error(response?.msg || '获取打印数据失败')
    }
  } catch (error) {
    console.error('获取打印数据失败', error)
    ElMessage.error('获取打印数据失败')
  }
}

// 面单拼接
const handleWaybillMerge = async (row) => {
  try {
    const params = {
      waveNo: row.waveNo,
      customerCode: row.customerCode || '',
      whCode: row.whCode || 'CA',
    }

    const response = await clickMergeLogistics(params)

    if (response && response.code === 200) {
      ElMessage.success('面单拼接成功')
    } else {
      ElMessage.error(response?.msg || '面单拼接失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('面单拼接失败', error)
      ElMessage.error('面单拼接失败')
    }
  }
}

// 二次分拣
const handleSecondSorting = (row) => {
  try {
    // 跳转到二次分拣页面，并传递波次号参数
    router.push({
      path: '/outbound/secondSort',
      query: { waveNo: row.waveNo },
    })
  } catch (error) {
    console.error('跳转二次分拣页面失败', error)
    ElMessage.error('跳转二次分拣页面失败')
  }
}

// 获取打印机列表
const getPrinterList = () => {
  if (!lodop.value) {
    console.error('Lodop 尚未准备好!')
    return
  }

  try {
    const printerCount = lodop.value.GET_PRINTER_COUNT()
    const printers = []

    for (let i = 0; i < printerCount; i++) {
      const printerName = lodop.value.GET_PRINTER_NAME(i)
      printers.push({
        index: i,
        name: printerName,
        value: i.toString(),
      })
    }

    printerList.value = printers

    // 选中默认打印机
    if (printers.length > 0) {
      const defaultPrintName = lodop.value.GET_PRINTER_NAME(-1)
      const defaultPrinter = printers.find((printer) => printer.name === defaultPrintName)
      if (defaultPrinter) {
        selectedPrinter.value = defaultPrinter.value
      }
    }
  } catch (error) {
    console.error('获取打印机列表失败:', error)
  }
}

// 处理打印机选择变更
const handlePrinterChange = (value) => {
  if (!lodop.value) {
    console.error('Lodop 尚未准备好!')
    return
  }

  try {
    const printerIndex = parseInt(value)
    lodop.value.SET_PRINTER_INDEXA(printerIndex)
    console.log('已设置打印机:', lodop.value.GET_PRINTER_NAME(printerIndex))
  } catch (error) {
    console.error('设置打印机失败:', error)
  }
}

// 关闭打印确认对话框
const handlePrintConfirmDialogClose = () => {
  printConfirmDialog.value.visible = false
  printConfirmDialog.value.printData = []
  printConfirmDialog.value.currentRow = null
}

// 确认打印 - 打开打印机选择对话框
const handlePrintConfirm = () => {
  // 先保存数据
  const printData = printConfirmDialog.value.printData
  const currentRow = printConfirmDialog.value.currentRow

  // 关闭确认对话框
  handlePrintConfirmDialogClose()

  // 打开打印机选择对话框
  printerSelectDialog.value.printData = printData
  printerSelectDialog.value.currentRow = currentRow
  printerSelectDialog.value.visible = true
}

// 关闭打印机选择对话框
const handlePrinterSelectDialogClose = () => {
  printerSelectDialog.value.visible = false
  printerSelectDialog.value.printData = []
  printerSelectDialog.value.currentRow = null
}

// 执行打印
const handleExecutePrint = async () => {
  try {
    // if (!lodop.value) {
    //   console.error('Lodop 尚未准备好!')
    //   return
    // }
    // const file = 'http://localhost:5173/a.pdf'
    // const response = await fetch(file)
    // const pdfBlob = await response.blob()
    // base64Pdf.value = await new Promise((resolve) => {
    //   const reader = new FileReader()
    //   reader.onload = () => resolve(reader.result)
    //   reader.readAsDataURL(pdfBlob)
    // })
    // console.log(base64Pdf.value)
    // lodop.value.PRINT_INIT('面单打印')
    // lodop.value.SET_LICENSES('', '', '', '')
    // lodop.value.ADD_PRINT_PDF(0, 0, '100%', '100%', base64Pdf.value.split(',')[1])
    // lodop.value.PRINT()
    handlePrinterSelectDialogClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('打印失败', error)
      ElMessage.error('打印失败')
    }
  }
}

// 初始化 Lodop
onMounted(async () => {
  installLodop()
  setTimeout(() => {
    const result = getLodop()
    lodopChecking.value = false // 检测完成
    if (result.error) {
      lodopError.value = result.error
      console.error(result.error)
      printLoading.value = false
      return
    }
    lodop.value = result.lodop
    printLoading.value = true
    // lodop初始化完成后获取打印机列表
    getPrinterList()
  }, 500)
})
</script>

<template>
  <el-card class="table-card" shadow="hover">
    <el-table
      :data="waveList"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />

      <!-- 动态渲染表格列 -->
      <template v-for="column in tableColumns" :key="column.prop">
        <!-- 波次单号列 - 特殊处理，显示为链接 -->
        <el-table-column
          v-if="column.prop === 'waveNo'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :fixed="column.fixed"
        >
          <template #default="scope">
            <el-link type="primary" @click="handleRowAction('view', scope.row)">
              {{ scope.row.waveNo }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 面单拼接 -->
        <el-table-column
          v-else-if="column.prop === 'mergeStatus'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ MERGE_STATUS_TEXT[scope.row.mergeStatus] }}
          </template>
        </el-table-column>

        <!-- 二次分拣 -->
        <el-table-column
          v-else-if="column.prop === 'sortingStatus'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ SORING_STATUS_TEXT[scope.row.sortingStatus] }}
          </template>
        </el-table-column>

        <!-- 复核 -->
        <el-table-column
          v-else-if="column.prop === 'reviewStatus'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ REVIEW_STATUS_TEXT[scope.row.reviewStatus] }}
          </template>
        </el-table-column>

        <!-- 出库 -->
        <el-table-column
          v-else-if="column.prop === 'outboundStatus'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ OUTBOUND_STATUS_TEXT[scope.row.outboundStatus] }}
          </template>
        </el-table-column>

        <!-- 拣货员 -->
        <el-table-column
          v-else-if="column.prop === 'assigntor'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.assigntor || '-' }}
          </template>
        </el-table-column>

        <!-- 已拣/应拣数量 -->
        <el-table-column
          v-else-if="column.prop === 'pickedQty'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.picked + '/' + scope.row.needPick }}
          </template>
        </el-table-column>

        <!-- 时间相关列 - 格式化显示 -->
        <el-table-column
          v-else-if="['createTime', 'assignTime', 'startTime', 'finishTime'].includes(column.prop)"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
        >
          <template #default="scope">
            {{ formatTime(scope.row[column.prop]) }}
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column
          v-else-if="column.prop === 'status'"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :fixed="column.fixed"
        >
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 其他普通列 -->
        <el-table-column
          v-else
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
        />
      </template>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <div class="action-buttons">
            <!-- 主要操作按钮（前两个） -->
            <el-button
              v-for="action in getGroupedActions(scope.row).primaryActions"
              :key="action.key"
              :type="action.type"
              :link="action.link"
              @click="handleRowAction(action.key, scope.row)"
            >
              {{ action.label }}
            </el-button>

            <!-- 更多操作下拉菜单 -->
            <el-dropdown
              v-if="getGroupedActions(scope.row).moreActions.length > 0"
              trigger="click"
              @command="(command) => handleRowAction(command, scope.row)"
            >
              <el-button :link="true" type="primary">
                更多
                <el-icon class="el-icon--right">
                  <MoreFilled />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="action in getGroupedActions(scope.row).moreActions"
                    :key="action.key"
                    :command="action.key"
                  >
                    {{ action.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>

  <!-- 面单打印确认对话框 -->
  <el-dialog
    v-model="printConfirmDialog.visible"
    title="面单打印确认"
    width="600px"
    @close="handlePrintConfirmDialogClose"
  >
    <div class="print-dialog-content">
      <div class="warning-message">
        <el-icon><Warning /></el-icon>
        <span>确认打印以下面单吗？</span>
      </div>

      <!-- 打印数据表格 -->
      <el-table :data="printConfirmDialog.printData" style="width: 100%; margin-top: 20px">
        <el-table-column label="出库单号" prop="sourceNo" />
        <el-table-column label="面单打印状态" prop="expressPrintStatusName" align="center" />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handlePrintConfirmDialogClose">取消</el-button>
        <el-button type="primary" @click="handlePrintConfirm"> 确认打印 </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 打印机选择对话框 -->
  <el-dialog
    v-model="printerSelectDialog.visible"
    title="选择打印机"
    width="500px"
    @close="handlePrinterSelectDialogClose"
  >
    <div class="printer-dialog-content">
      <div class="info-message">
        <span>波次：{{ printerSelectDialog.currentRow?.waveNo }}</span>
        <span>共 {{ printerSelectDialog.printData?.length || 0 }} 个面单</span>
      </div>

      <!-- 打印机选择 -->
      <div class="printer-selection" v-if="printerList.length > 0">
        <label>选择打印机：</label>
        <el-select
          v-model="selectedPrinter"
          placeholder="请选择打印机"
          @change="handlePrinterChange"
          style="width: 100%"
        >
          <el-option
            v-for="printer in printerList"
            :key="printer.value"
            :label="printer.name"
            :value="printer.value"
          />
        </el-select>
      </div>

      <!-- Lodop 错误提示 -->
      <div v-if="lodopError" class="lodop-error">
        <el-alert :title="lodopError" type="error" show-icon :closable="false" />
      </div>

      <!-- Lodop 检测中提示 -->
      <div v-if="lodopChecking" class="lodop-checking">
        <el-alert title="正在检测打印控件..." type="info" show-icon :closable="false" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handlePrinterSelectDialogClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleExecutePrint"
          :disabled="!lodop || lodopError || !selectedPrinter"
        >
          开始打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.table-card {
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page) !important;
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600 !important;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
}

/* 打印对话框样式 */
.print-dialog-content {
  padding: 10px 0;
}

.warning-message {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-warning);
}

/* 打印机选择对话框样式 */
.printer-dialog-content {
  padding: 10px 0;
}

.info-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--el-bg-color-page);
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.printer-selection {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.printer-selection label {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.lodop-error,
.lodop-checking {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
