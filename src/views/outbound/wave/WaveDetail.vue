<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DocumentCopy, MoreFilled, Warning } from '@element-plus/icons-vue'
import {
  getWaveDetail,
  getWaveDownload,
  getPreviewAndDownLoadUrl,
  listReprintOrder,
  clickMergeLogistics,
} from '@/api/outbound/wave'
import { installLodop, getLodop } from '@/utils/lodop/lodop'
import { useWaveConfig, useWaveStatus } from '@/composables/useWaveConfig'
import WaveBasicInfo from './components/WaveBasicInfo.vue'
import WaveOrderList from './components/WaveOrderList.vue'
import WaveSkuDetail from './components/WaveSkuDetail.vue'
import WaveOperationLog from './components/WaveOperationLog.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 波次详情数据
const loading = ref(false)
const waveDetail = ref({})
const waveNo = ref(route.params.waveNo || route.query.waveNo)

// 标签页
const activeTab = ref('orders')

// 打印相关状态
const lodop = ref(null)
const lodopError = ref('')
const lodopChecking = ref(true)
const printLoading = ref(false)
const printerList = ref([])
const selectedPrinter = ref('')

// 打印确认对话框状态
const printConfirmDialog = ref({
  visible: false,
  printData: [],
  currentRow: null,
})

// 打印机选择对话框状态
const printerSelectDialog = ref({
  visible: false,
  printData: [],
  currentRow: null,
})

// 使用配置组合式函数
const { getVisibleDetailActions } = useWaveConfig()
const { getStatusTagType } = useWaveStatus()

// 获取波次详情
const fetchWaveDetail = async () => {
  if (!waveNo.value) {
    ElMessage.error('波次ID不能为空')
    return
  }

  loading.value = true

  const params = {
    waveNo: waveNo.value,
    whCode: 'CA',
  }

  try {
    const response = await getWaveDetail(params)

    if (response && response.code === 200) {
      waveDetail.value = response.data || {}
    }
  } catch (error) {
    console.error('获取波次详情失败', error)
    ElMessage.error('获取波次详情失败')
  } finally {
    loading.value = false
  }
}

// 复制波次号
const copyWaveNo = async () => {
  try {
    await navigator.clipboard.writeText(waveDetail.value.waveNo)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败', error)
    ElMessage.error('复制失败')
  }
}

// 按钮分组逻辑
const getGroupedDetailActions = (waveDetail) => {
  const allActions = getVisibleDetailActions(waveDetail)
  if (allActions.length <= 4) {
    return {
      primaryActions: allActions,
      moreActions: [],
    }
  }
  // 保留最右边的3个按钮，将左边的按钮放入下拉菜单
  const splitIndex = allActions.length - 3
  return {
    primaryActions: allActions.slice(splitIndex), // 最右边的3个
    moreActions: allActions.slice(0, splitIndex), // 左边的按钮放入下拉
  }
}

// 打印汇总拣货单
const handleWaveDownload = async (row, printType) => {
  let iframe = null

  try {
    const params = {
      customerCode: '',
      printType: printType,
      waveNo: row.waveNo,
      whCode: row.whCode,
    }

    // 1. 调用接口获取PDF的Blob数据
    const response = await getWaveDownload(params)
    const blob = new Blob([response.data], { type: 'application/pdf' })
    // 检查 blob 是否为空，避免创建空的打印页
    if (blob.size === 0) {
      console.error('获取到的PDF文件内容为空')
      return
    }

    const blobUrl = URL.createObjectURL(blob)

    iframe = document.createElement('iframe')
    iframe.style.display = 'none'
    iframe.src = blobUrl

    const cleanup = () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl)
        blobUrl = null
      }
      if (iframe && iframe.parentNode) {
        document.body.removeChild(iframe)
        iframe = null
      }
    }

    iframe.onload = () => {
      try {
        iframe.contentWindow.focus()
        iframe.contentWindow.print()
        if (iframe.contentWindow.onafterprint !== undefined) {
          iframe.contentWindow.onafterprint = () => {
            cleanup()
          }
        } else {
          setTimeout(cleanup, 500)
        }
      } catch (e) {
        console.error('调用打印功能失败:', e)
        ElMessage.error('调用打印功能失败')
        cleanup()
      }
    }
    // 将 iframe 添加到 DOM 中
    document.body.appendChild(iframe)
  } catch (error) {
    console.error('获取或处理PDF文件时出错:', error)
    ElMessage.error('获取打印文件失败')
    // 如果出错时iframe已创建，也尝试清理
    if (iframe && iframe.parentNode) {
      document.body.removeChild(iframe)
    }
    if (blobUrl) {
      URL.revokeObjectURL(blobUrl)
    }
  }
}

// 面单查看
const handleWaybillView = async (row) => {
  try {
    const params = {
      customerCode: row.customerCode || '',
      fileKey: row.fileKey || '',
      fileName: row.fileName || '',
      whCode: row.whCode || 'CA',
    }

    const response = await getPreviewAndDownLoadUrl(params)

    if (response && response.code === 200 && response.data?.previewUrl) {
      // 在新窗口打开预览URL
      window.open(response.data.previewUrl, '_blank')
    } else {
      ElMessage.error(response?.msg || '查看面单失败')
    }
  } catch (error) {
    console.error('查看面单失败', error)
    ElMessage.error('查看面单失败')
  }
}

// 面单打印
const handleWaybillPrint = async (row) => {
  try {
    const params = {
      waveNo: row.waveNo,
      whCode: row.whCode || 'CA',
    }

    const response = await listReprintOrder(params)

    if (response && response.code === 200 && response.data) {
      printConfirmDialog.value.printData = response.data
      printConfirmDialog.value.currentRow = row
      printConfirmDialog.value.visible = true
    } else {
      ElMessage.error(response?.msg || '获取打印数据失败')
    }
  } catch (error) {
    console.error('获取打印数据失败', error)
    ElMessage.error('获取打印数据失败')
  }
}

// 面单拼接
const handleWaybillMerge = async (row) => {
  try {
    const params = {
      waveNo: row.waveNo,
      customerCode: row.customerCode || '',
      whCode: row.whCode || 'CA',
    }

    const response = await clickMergeLogistics(params)

    if (response && response.code === 200) {
      ElMessage.success('面单拼接成功')
    } else {
      ElMessage.error(response?.msg || '面单拼接失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('面单拼接失败', error)
      ElMessage.error('面单拼接失败')
    }
  }
}

// 二次分拣
const handleSecondSorting = (row) => {
  try {
    // 跳转到二次分拣页面，并传递波次号参数
    router.push({
      path: '/outbound/secondSort',
      query: { waveNo: row.waveNo },
    })
  } catch (error) {
    console.error('跳转二次分拣页面失败', error)
    ElMessage.error('跳转二次分拣页面失败')
  }
}

// 处理操作按钮点击
const handleAction = (action) => {
  switch (action.key) {
    case 'assign':
      ElMessage.info('分配拣货员功能待实现')
      break
    case 'waybill_view':
      handleWaybillView(waveDetail.value)
      break
    case 'waybill_print':
      handleWaybillPrint(waveDetail.value)
      break
    case 'waybill_merge':
      handleWaybillMerge(waveDetail.value)
      break
    case 'second_sorting':
      handleSecondSorting(waveDetail.value)
      break
    case 'quick_outbound':
      ElMessage.info('快捷出库功能待实现')
      break
    case 'print_sorting':
      handleWaveDownload(waveDetail.value, 2) // 打印分拣拣货单
      break
    case 'print_summary':
      handleWaveDownload(waveDetail.value, 1) // 打印汇总拣货单
      break
    case 'picking':
      ElMessage.info('拣货功能待实现')
      break
    default:
      console.warn(`未知的操作: ${action.key}`)
  }
}

// 获取打印机列表
const getPrinterList = () => {
  if (!lodop.value) {
    console.error('Lodop 尚未准备好!')
    return
  }

  try {
    const printerCount = lodop.value.GET_PRINTER_COUNT()
    const printers = []

    for (let i = 0; i < printerCount; i++) {
      const printerName = lodop.value.GET_PRINTER_NAME(i)
      printers.push({
        index: i,
        name: printerName,
        value: i.toString(),
      })
    }

    printerList.value = printers

    // 选中默认打印机
    if (printers.length > 0) {
      const defaultPrintName = lodop.value.GET_PRINTER_NAME(-1)
      const defaultPrinter = printers.find((printer) => printer.name === defaultPrintName)
      if (defaultPrinter) {
        selectedPrinter.value = defaultPrinter.value
      }
    }
  } catch (error) {
    console.error('获取打印机列表失败:', error)
  }
}

// 处理打印机选择变更
const handlePrinterChange = (value) => {
  if (!lodop.value) {
    console.error('Lodop 尚未准备好!')
    return
  }

  try {
    const printerIndex = parseInt(value)
    lodop.value.SET_PRINTER_INDEXA(printerIndex)
    console.log('已设置打印机:', lodop.value.GET_PRINTER_NAME(printerIndex))
  } catch (error) {
    console.error('设置打印机失败:', error)
  }
}

// 关闭打印确认对话框
const handlePrintConfirmDialogClose = () => {
  printConfirmDialog.value.visible = false
  printConfirmDialog.value.printData = []
  printConfirmDialog.value.currentRow = null
}

// 确认打印 - 打开打印机选择对话框
const handlePrintConfirm = () => {
  // 先保存数据
  const printData = printConfirmDialog.value.printData
  const currentRow = printConfirmDialog.value.currentRow

  // 关闭确认对话框
  handlePrintConfirmDialogClose()

  // 打开打印机选择对话框
  printerSelectDialog.value.printData = printData
  printerSelectDialog.value.currentRow = currentRow
  printerSelectDialog.value.visible = true
}

// 关闭打印机选择对话框
const handlePrinterSelectDialogClose = () => {
  printerSelectDialog.value.visible = false
  printerSelectDialog.value.printData = []
  printerSelectDialog.value.currentRow = null
}

// 执行打印
const handleExecutePrint = async () => {
  try {
    if (!lodop.value) {
      console.error('Lodop 尚未准备好!')
      return
    }

    // 这里应该调用实际的打印逻辑
    // 由于面单打印通常需要特定的打印格式和数据，这里先显示成功消息
    ElMessage.success('面单打印成功')
    handlePrinterSelectDialogClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('打印失败', error)
      ElMessage.error('打印失败')
    }
  }
}

// 查看订单详情
const handleViewOrder = (order) => {
  ElMessage.info(`查看订单详情: ${order.orderNo}`)
}

// 监听路由参数变化
watch(
  () => route.query.waveNo,
  (newWaveNo) => {
    if (newWaveNo && newWaveNo !== waveNo.value) {
      waveNo.value = newWaveNo
      fetchWaveDetail()
    }
  },
  { immediate: false },
)

// 监听路由参数变化（params方式）
watch(
  () => route.params.waveNo,
  (newWaveNo) => {
    if (newWaveNo && newWaveNo !== waveNo.value) {
      waveNo.value = newWaveNo
      fetchWaveDetail()
    }
  },
  { immediate: false },
)

// 初始化
onMounted(() => {
  fetchWaveDetail()

  // 初始化 Lodop
  installLodop()
  setTimeout(() => {
    const result = getLodop()
    lodopChecking.value = false // 检测完成
    if (result.error) {
      lodopError.value = result.error
      console.error(result.error)
      printLoading.value = false
      return
    }
    lodop.value = result.lodop
    printLoading.value = true
    // lodop初始化完成后获取打印机列表
    getPrinterList()
  }, 500)
})
</script>

<template>
  <div class="wave-detail-container" v-loading="loading">
    <!-- 顶部操作栏 -->
    <div class="detail-header">
      <div class="header-title">
        <div class="wave-no-container">
          <span class="wave-no">{{ waveDetail.waveNo }}</span>
          <el-icon @click="copyWaveNo" class="copy-icon">
            <DocumentCopy />
          </el-icon>
          <el-tag :type="getStatusTagType(waveDetail.status)" class="status-tag">
            {{ waveDetail.statusName }}
          </el-tag>
        </div>
      </div>
      <div class="header-actions">
        <!-- 更多操作下拉菜单（放在最左边） -->
        <el-dropdown
          v-if="getGroupedDetailActions(waveDetail).moreActions.length > 0"
          trigger="click"
          @command="(command) => handleAction({ key: command })"
        >
          <el-button>
            更多
            <el-icon class="el-icon--right">
              <MoreFilled />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="action in getGroupedDetailActions(waveDetail).moreActions"
                :key="action.key"
                :command="action.key"
              >
                {{ action.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 主要操作按钮（前3个） -->
        <el-button
          style="margin-left: 0px"
          v-for="(action, index) in getGroupedDetailActions(waveDetail).primaryActions"
          :key="action.key"
          :type="
            index === getGroupedDetailActions(waveDetail).primaryActions.length - 1
              ? 'primary'
              : action.type
          "
          @click="handleAction(action)"
        >
          {{ action.label }}
        </el-button>
      </div>
    </div>

    <!-- 基本信息 -->
    <WaveBasicInfo :waveDetail="waveDetail" />

    <!-- 详情标签页 -->
    <el-card class="detail-tabs-card" shadow="hover">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="出库单信息" name="orders">
          <WaveOrderList :waveNo="waveNo" @view-order="handleViewOrder" />
        </el-tab-pane>

        <el-tab-pane label="产品明细" name="skus">
          <WaveSkuDetail :waveNo="waveNo" />
        </el-tab-pane>

        <el-tab-pane label="日志" name="logs">
          <WaveOperationLog :waveNo="waveNo" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>

  <!-- 面单打印确认对话框 -->
  <el-dialog
    v-model="printConfirmDialog.visible"
    title="面单打印确认"
    width="600px"
    @close="handlePrintConfirmDialogClose"
  >
    <div class="print-dialog-content">
      <div class="warning-message">
        <el-icon><Warning /></el-icon>
        <span>确认打印以下面单吗？</span>
      </div>

      <!-- 打印数据表格 -->
      <el-table :data="printConfirmDialog.printData" style="width: 100%; margin-top: 20px">
        <el-table-column label="出库单号" prop="sourceNo" />
        <el-table-column label="面单打印状态" prop="expressPrintStatusName" align="center" />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handlePrintConfirmDialogClose">取消</el-button>
        <el-button type="primary" @click="handlePrintConfirm"> 确认打印 </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 打印机选择对话框 -->
  <el-dialog
    v-model="printerSelectDialog.visible"
    title="选择打印机"
    width="500px"
    @close="handlePrinterSelectDialogClose"
  >
    <div class="printer-dialog-content">
      <div class="info-message">
        <span>波次：{{ printerSelectDialog.currentRow?.waveNo }}</span>
        <span>共 {{ printerSelectDialog.printData?.length || 0 }} 个面单</span>
      </div>

      <!-- 打印机选择 -->
      <div class="printer-selection" v-if="printerList.length > 0">
        <label>选择打印机：</label>
        <el-select
          v-model="selectedPrinter"
          placeholder="请选择打印机"
          @change="handlePrinterChange"
          style="width: 100%"
        >
          <el-option
            v-for="printer in printerList"
            :key="printer.value"
            :label="printer.name"
            :value="printer.value"
          />
        </el-select>
      </div>

      <!-- Lodop 错误提示 -->
      <div v-if="lodopError" class="lodop-error">
        <el-alert :title="lodopError" type="error" show-icon :closable="false" />
      </div>

      <!-- Lodop 检测中提示 -->
      <div v-if="lodopChecking" class="lodop-checking">
        <el-alert title="正在检测打印控件..." type="info" show-icon :closable="false" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handlePrinterSelectDialogClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleExecutePrint"
          :disabled="!lodop || lodopError || !selectedPrinter"
        >
          开始打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.wave-detail-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 120px);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.wave-no-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
}

.wave-no {
  font-weight: 700;
  font-size: 18px;
}

.copy-icon {
  color: var(--el-color-primary);
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s;
}

.copy-icon:hover {
  color: var(--el-color-primary-dark-2);
}

.status-tag {
  margin-left: 4px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.detail-tabs-card {
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

:deep(.el-card__body) {
  padding: 0;
}

:deep(.el-tabs) {
  margin: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-tabs__nav-wrap) {
  padding: 16px 0;
}

/* 打印对话框样式 */
.print-dialog-content {
  padding: 10px 0;
}

.warning-message {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-warning);
}

/* 打印机选择对话框样式 */
.printer-dialog-content {
  padding: 10px 0;
}

.info-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--el-bg-color-page);
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.printer-selection {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.printer-selection label {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.lodop-error,
.lodop-checking {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
