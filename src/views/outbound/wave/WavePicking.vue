<script setup>
import { ref, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'

// 路由
const route = useRoute()
const router = useRouter()

// 波次号
const waveNo = ref(route.query.waveNo || '')

// 页面加载状态
const loading = ref(false)

// 波次基本信息 - 模拟数据
const waveDetail = ref({
  waveNo: 'W250729AXO',
  status: 20,
  statusName: '拣货中',
  orderCount: 1,
  planQty: 2,
  pickQty: 0,
  assigntor: '一号头',
  createTime: '2024-07-29 10:30:00',
  pickingTypeName: '一单多件',
})

// 产品列表 - 模拟数据
const productList = ref([
  {
    id: 1,
    productSku: 'demo2',
    productSkuName: 'demo2',
    productName: 'demo2',
    planQty: 1,
    pickQty: 0,
    remainQty: 1,
    stockQualityName: '正常',
    stockQuality: 0,
    pickingQty: 1,
    pickingLocation: '',
    stockCount: 56,
  },
  {
    id: 2,
    productSku: 'demo3',
    productSkuName: 'demo3',
    productName: 'demo3',
    planQty: 2,
    pickQty: 0,
    remainQty: 2,
    stockQualityName: '正常',
    stockQuality: 0,
    pickingQty: 1,
    pickingLocation: '',
    stockCount: 120,
  },
  {
    id: 3,
    productSku: 'demo4',
    productSkuName: 'demo4',
    productName: 'demo4',
    planQty: 3,
    pickQty: 1,
    remainQty: 2,
    stockQualityName: '正常',
    stockQuality: 0,
    pickingQty: 0,
    pickingLocation: '',
    stockCount: 88,
  },
  {
    id: 4,
    productSku: 'demo5',
    productSkuName: 'demo5',
    productName: 'demo5',
    planQty: 1,
    pickQty: 0,
    remainQty: 1,
    stockQualityName: '正常',
    stockQuality: 0,
    pickingQty: 0,
    pickingLocation: '',
    stockCount: 45,
  },
  {
    id: 5,
    productSku: 'demo6',
    productSkuName: 'demo6',
    productName: 'demo6',
    planQty: 4,
    pickQty: 2,
    remainQty: 2,
    stockQualityName: '正常',
    stockQuality: 0,
    pickingQty: 0,
    pickingLocation: '',
    stockCount: 200,
  },
])

const selectedProducts = ref([])

// 库位设置
const locationSettings = ref([
  {
    id: 1,
    cellNo: 'TMP',
    areaType: 1,
    areaTypeName: '拣选区',
    cellType: 1,
    cellTypeName: '货架',
  },
])

// 复制波次号
const copyWaveNo = async () => {
  try {
    await navigator.clipboard.writeText(waveDetail.value.waveNo)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败', error)
    ElMessage.error('复制失败')
  }
}

// 处理产品选择
const handleProductSelection = (selection) => {
  selectedProducts.value = selection
}

// 处理拣货数量变化
const handlePickingQtyChange = (row, value) => {
  if (value < 0) {
    row.pickingQty = 0
    ElMessage.warning('拣货数量不能小于0')
    return
  }

  if (value > row.remainQty) {
    row.pickingQty = row.remainQty
    ElMessage.warning('拣货数量不能大于剩余数量')
    return
  }

  row.pickingQty = value
}

// 确认拣货
const confirmPicking = async () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请选择要拣货的产品')
    return
  }

  const hasPickingQty = selectedProducts.value.some((item) => item.pickingQty > 0)
  if (!hasPickingQty) {
    ElMessage.warning('请填写拣货数量')
    return
  }

  if (locationSettings.value.length === 0) {
    ElMessage.warning('请设置拣货库位')
    return
  }

  try {
    await ElMessageBox.confirm('确认提交拣货信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // TODO: 调用拣货API
    ElMessage.success('拣货信息提交成功')

    // 返回波次列表
    router.push('/outbound/wave')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交拣货信息失败', error)
      ElMessage.error('提交拣货信息失败')
    }
  }
}

// 返回
const goBack = () => {
  router.go(-1)
}
</script>

<template>
  <div class="wave-picking-container" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="wave-title">
          {{ waveDetail.waveNo }}
          <el-icon @click="copyWaveNo">
            <DocumentCopy style="width: 1em; height: 1em; margin-right: 8px; cursor: pointer" />
          </el-icon>
        </h2>
        <div class="wave-stats">
          <span>波次品种类型：{{ waveDetail.orderCount }}</span>
          <span>出库单类型：{{ waveDetail.orderCount }}</span>
          <span>订单数：{{ waveDetail.orderCount }}</span>
          <span>产品数：{{ waveDetail.planQty }}</span>
        </div>
      </div>
    </div>

    <!-- 产品列表 -->
    <el-card class="product-card" shadow="hover">
      <el-table
        :data="productList"
        @selection-change="handleProductSelection"
        stripe
        style="width: 100%"
        height="100%"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="productSku" label="SKU" width="180" />

        <el-table-column prop="productSkuName" label="产品条码" width="200" />

        <el-table-column prop="productName" label="产品名称" width="180" />

        <el-table-column label="已拣/应拣" width="150" align="center">
          <template #default="scope">
            <span>{{ scope.row.pickQty }}/{{ scope.row.planQty }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="stockQualityName" label="库存属性" width="150" align="center" />

        <el-table-column label="拣货库位" width="220" align="center">
          <template #default="scope">
            <el-select
              v-model="scope.row.pickingLocation"
              placeholder="选择库位"
              style="width: 100%"
            >
              <el-option
                v-for="location in locationSettings"
                :key="location.cellNo"
                :label="location.cellNo"
                :value="location.cellNo"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column prop="stockCount" label="库存产品数量" width="150" align="center">
          <template #default="scope">
            <span class="stock-count">{{ scope.row.stockCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="拣货数量" align="center">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.pickingQty"
              :min="1"
              :max="scope.row.remainQty"
              @change="(value) => handlePickingQtyChange(scope.row, value)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 底部操作按钮 -->
    <el-card class="footer-actions-card" shadow="hover">
      <div class="footer-actions">
        <el-button size="large" @click="goBack">取消</el-button>
        <el-button type="primary" size="large" @click="confirmPicking">确认</el-button>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.wave-picking-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  padding: 20px;
  background-color: var(--el-bg-color-page);
  overflow: hidden;
}

.page-header {
  flex-shrink: 0;
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.wave-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.wave-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.product-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.product-card :deep(.el-card__body) {
  padding: 20px !important;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
}

.product-card .el-table {
  flex: 1;
  overflow: auto;
}

.stock-count {
  color: var(--el-color-primary);
  font-weight: 500;
}

.footer-actions-card {
  flex-shrink: 0;
  margin-top: 20px;
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 10px 0;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page) !important;
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600 !important;
}
</style>
