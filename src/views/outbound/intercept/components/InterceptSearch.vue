<script setup>
import { Search, Refresh } from '@element-plus/icons-vue'
import { defineProps, defineEmits, reactive, ref } from 'vue'
import DateTypeRangePicker from '@/components/DateTypeRangePicker.vue'
import NumberRange from '@/components/NumberRangeInput.vue'

defineProps({
  queryParams: {
    type: Object,
    required: {},
  },
  dateTypeOptions: {
    type: Array,
    required: true,
  },
  dateParams: {
    type: Array,
    required: true,
  },
  orderNoOptions: {
    type: Array,
    required: true,
  },
  consumerList: {
    type: Array,
    required: true,
  },
  channelList: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['search', 'reset'])

const handleSearch = () => {
  emit('search')
}

const selVal = ref(null)
const handleSelFocus = (val) => {
  selVal.value = val
}

const handleSelBlur = (val) => {
  if (JSON.stringify(val) !== JSON.stringify(selVal.value)) {
    handleSearch()
  }
}

const handleReset = () => {
  emit('reset')
}
</script>

<template>
  <el-card class="search-card" shadow="hover">
    <el-form :model="queryParams" inline>
      <el-form-item label="" prop="consumer">
        <el-select
          v-model="queryParams.customerCodes"
          @focus="handleSelFocus(queryParams.customerCodes)"
          @blur="handleSelBlur(queryParams.customerCodes)"
          multiple
          placeholder="客户名称/代码"
          style="width: 150px"
          filterable
        >
          <el-option
            v-for="dict in consumerList"
            :key="dict.customerCode"
            :label="dict.customerName"
            :value="dict.customerCode"
          >
            <span style="float: left">{{ dict.customerName }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
              {{ dict.customerCode }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="channel">
        <el-select
          v-model="queryParams.logisticsChannel"
          @focus="handleSelFocus(queryParams.logisticsChannel)"
          @blur="handleSelBlur(queryParams.logisticsChannel)"
          multiple
          placeholder="物流渠道"
          style="width: 150px"
          filterable
        >
          <el-option
            v-for="dict in channelList"
            :key="dict.logisticsChannel"
            :label="dict.logisticsChannelName"
            :value="dict.logisticsChannel"
          >
            <span style="float: left">{{ dict.logisticsChannelName }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
              {{ dict.logisticsChannel }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="dateRange" class="search-item">
        <DateTypeRangePicker
          style="width: 450px"
          v-model:dateType="dateParams.dateType"
          v-model:dateRange="dateParams.dateRange"
          :dateTypeOptions="dateTypeOptions"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>

      <el-form-item prop="searchValue" class="search-item">
        <el-input
          v-model="queryParams.sourceNoVal"
          placeholder="请输入搜索内容"
          clearable
          style="width: 240px"
          class="input-with-select"
          @keyup.enter="handleSearch"
        >
          <template #prepend>
            <el-select v-model="queryParams.orderNoType" style="width: 100px">
              <el-option
                v-for="option in orderNoOptions"
                :key="option.key"
                :label="option.val"
                :value="option.key"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <br />
      <el-form-item>
        <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
        <el-button :icon="Refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<style scoped>
.search-card {
  margin-bottom: 20px;
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}
</style>
