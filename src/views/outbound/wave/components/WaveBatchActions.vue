<script setup>
import { ArrowDown, MoreFilled } from '@element-plus/icons-vue'
import { defineProps, defineEmits, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useWaveConfig } from '@/composables/useWaveConfig'

const props = defineProps({
  selectedRows: {
    type: Array,
    default: () => [],
  },
  pickerOptions: {
    type: Array,
    default: () => [],
  },
  currentTab: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['batch-assign', 'batch-start', 'batch-finish', 'batch-print', 'export'])

// 使用配置组合式函数
const { getGroupedBatchActions } = useWaveConfig()

// 分配拣货员对话框
const assignDialog = ref({
  visible: false,
  pickerCode: '',
})

// 批量分配拣货员
const handleBatchAssign = () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要分配的波次')
    return
  }
  assignDialog.value.visible = true
  assignDialog.value.pickerCode = ''
}

// 确认分配
const confirmAssign = () => {
  if (!assignDialog.value.pickerCode) {
    ElMessage.warning('请选择拣货员')
    return
  }

  const waveIds = props.selectedRows.map((row) => row.id)
  emit('batch-assign', {
    waveIds,
    pickerCode: assignDialog.value.pickerCode,
  })
  assignDialog.value.visible = false
}

// 批量开始拣货
const handleBatchStart = () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要开始拣货的波次')
    return
  }

  const waveIds = props.selectedRows.map((row) => row.id)
  emit('batch-start', { waveIds })
}

// 批量完成拣货
const handleBatchFinish = () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要完成拣货的波次')
    return
  }

  const waveIds = props.selectedRows.map((row) => row.id)
  emit('batch-finish', { waveIds })
}

// 批量打印拣货单
const handleBatchPrint = () => {
  if (props.selectedRows.length === 0) {
    ElMessage.warning('请先选择要打印的波次')
    return
  }

  const waveIds = props.selectedRows.map((row) => row.id)
  emit('batch-print', { waveIds })
}

// 导出数据
const handleExport = () => {
  emit('export')
}

// 统一的批量操作处理
const handleBatchAction = (actionKey) => {
  const actionMap = {
    'assign_picker': handleBatchAssign,
    'batch_start': handleBatchStart,
    'batch_finish': handleBatchFinish,
    'batch_print_summary': handleBatchPrint,
    'batch_print_sorting': handleBatchPrint,
    'export_selected': handleExport,
    'export_filtered': handleExport,
  }

  const action = actionMap[actionKey]
  if (action) {
    action()
  } else {
    console.warn(`未知的批量操作: ${actionKey}`)
  }
}

// 处理更多操作菜单的点击
const handleMoreAction = (actionKey) => {
  handleBatchAction(actionKey)
}

// 检查操作是否可以执行
const canExecuteAction = (action) => {
  // 导出操作不需要选中数据
  if (action.key === 'export_filtered' || action.group === 'export') {
    return true
  }
  // 其他操作需要选中数据
  return props.selectedRows.length > 0
}
</script>

<template>
  <el-card class="batch-actions-card" shadow="hover">
    <div class="action-buttons">
      <!-- 主要操作区域 -->
      <template v-for="action in getGroupedBatchActions(currentTab).primaryActions" :key="action.key">
        <!-- 有子项的操作显示为下拉菜单 -->
        <el-dropdown
          v-if="action.children && action.children.length > 0"
          @command="handleBatchAction"
        >
          <el-button :type="action.type" :disabled="!canExecuteAction(action)">
            {{ action.label }}
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="child in action.children.filter(c => c.visible)"
                :key="child.key"
                :command="child.key"
              >
                {{ child.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 没有子项的操作直接显示为按钮 -->
        <el-button
          v-else
          :type="action.type"
          :disabled="!canExecuteAction(action)"
          @click="handleBatchAction(action.key)"
        >
          {{ action.label }}
        </el-button>
      </template>

      <!-- 更多操作下拉菜单 -->
      <el-dropdown
        v-if="getGroupedBatchActions(currentTab).moreActions.length > 0"
        @command="handleMoreAction"
      >
        <el-button>
          更多操作
          <el-icon class="dropdown-icon"><MoreFilled /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <template v-for="action in getGroupedBatchActions(currentTab).moreActions" :key="action.key">
              <!-- 如果更多操作中没有子项，直接显示 -->
              <el-dropdown-item
                v-if="!action.children"
                :command="action.key"
              >
                {{ action.label }}
              </el-dropdown-item>
              <!-- 有子项的操作在更多菜单中展开显示 -->
              <template v-else>
                <el-dropdown-item disabled class="group-header">
                  {{ action.label }}
                </el-dropdown-item>
                <el-dropdown-item
                  v-for="child in action.children.filter(c => c.visible)"
                  :key="child.key"
                  :command="child.key"
                  class="sub-item"
                >
                  {{ child.label }}
                </el-dropdown-item>
              </template>
            </template>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 分配拣货员对话框 -->
    <el-dialog
      v-model="assignDialog.visible"
      title="批量分配拣货员"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="拣货员">
          <el-select
            v-model="assignDialog.pickerCode"
            placeholder="请选择拣货员"
            style="width: 100%"
          >
            <el-option
              v-for="picker in pickerOptions"
              :key="picker.code"
              :label="picker.name"
              :value="picker.code"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmAssign">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<style scoped>
.batch-actions-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.dropdown-icon {
  margin-left: 4px;
  font-size: 12px;
}

.group-header {
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.sub-item {
  padding-left: 20px;
}

:deep(.el-card__body) {
  padding: 16px 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
