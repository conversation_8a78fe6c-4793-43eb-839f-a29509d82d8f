/**
 * 拣货波次配置相关组合式函数
 */
import { reactive, readonly, toRefs } from 'vue'
import {
  WAVE_STATUS,
  WAVE_STATUS_OPTIONS,
  STATUS_TAG_TYPE_MAP,
  BASE_TABLE_COLUMNS,
  STATUS_SPECIFIC_COLUMNS,
  ROW_ACTIONS,
  BATCH_ACTIONS,
  DETAIL_ACTIONS
} from '@/constants/wave'
import { channels, options } from '@/api/outbound/parcel'

/**
 * 波次配置管理
 */
export function useWaveConfig() {
  // 获取指定行的可见操作按钮
  const getVisibleRowActions = (row) => {
    return Object.values(ROW_ACTIONS).filter(action => action.visible(row))
  }

  // 获取指定标签页的可见批量操作按钮
  const getVisibleBatchActions = (currentTab) => {
    // 将字符串状态转换为数字，以便与 WAVE_STATUS 常量进行比较
    const numericTab = currentTab === '' ? '' : Number(currentTab)
    return Object.values(BATCH_ACTIONS).filter(action => action.visible(numericTab))
  }

  // 获取指定波次详情的可见操作按钮
  const getVisibleDetailActions = (waveDetail) => {
    return Object.values(DETAIL_ACTIONS).filter(action => action.visible(waveDetail))
  }

  // 混合模式：获取分组后的批量操作按钮
  const getGroupedBatchActions = (currentTab) => {
    // 1. 获取可见操作并按优先级排序
    const visibleActions = getVisibleBatchActions(currentTab)
      .sort((a, b) => (a.priority || 999) - (b.priority || 999))

    // 2. 数量控制阈值
    const maxPrimaryActions = 3

    if (visibleActions.length <= maxPrimaryActions) {
      // 操作数量不多，全部显示为主要操作
      return {
        primaryActions: visibleActions,
        moreActions: []
      }
    }

    // 3. 超过阈值时分组
    return {
      primaryActions: visibleActions.slice(0, 2), // 前2个高优先级操作
      moreActions: visibleActions.slice(2) // 其余操作放入"更多操作"下拉菜单
    }
  }

  // 根据状态获取表格列配置
  const getTableColumns = (tabStatus) => {
    // 将字符串状态转换为数字，以便与 STATUS_SPECIFIC_COLUMNS 的键进行匹配
    const numericStatus = tabStatus === '' ? '' : Number(tabStatus)
    const specificColumns = STATUS_SPECIFIC_COLUMNS[numericStatus] || STATUS_SPECIFIC_COLUMNS['']
    return [...BASE_TABLE_COLUMNS, ...specificColumns]
  }

  return {
    getVisibleRowActions,
    getVisibleBatchActions,
    getVisibleDetailActions,
    getGroupedBatchActions,
    getTableColumns
  }
}

/**
 * 波次状态工具方法
 */
export function useWaveStatus() {
  // 获取状态标签类型
  const getStatusTagType = (status) => {
    return STATUS_TAG_TYPE_MAP[status] || 'info'
  }

  // 获取状态显示名称
  const getStatusLabel = (status) => {
    const option = WAVE_STATUS_OPTIONS.find(item => item.value === status)
    return option ? option.label : '未知状态'
  }

  // 获取状态颜色
  const getStatusColor = (status) => {
    const option = WAVE_STATUS_OPTIONS.find(item => item.value === status)
    return option ? option.color : '#909399'
  }

  // 格式化时间显示
  const formatTime = (time) => {
    return time || '-'
  }

  // 检查状态是否可以执行指定操作
  const canExecuteAction = (status, action) => {
    const actionMap = {
      assign: [WAVE_STATUS.PENDING],
      start: [WAVE_STATUS.PENDING],
      finish: [WAVE_STATUS.PICKING],
      print: [WAVE_STATUS.PENDING, WAVE_STATUS.PICKING]
    }

    return actionMap[action]?.includes(status) || false
  }

  // 获取下一个状态
  const getNextStatus = (currentStatus) => {
    const statusFlow = {
      [WAVE_STATUS.PENDING]: WAVE_STATUS.PICKING,
      [WAVE_STATUS.PICKING]: WAVE_STATUS.COMPLETED
    }

    return statusFlow[currentStatus] || currentStatus
  }

  // 验证状态转换是否有效
  const isValidStatusTransition = (fromStatus, toStatus) => {
    const validTransitions = {
      [WAVE_STATUS.PENDING]: [WAVE_STATUS.PICKING],
      [WAVE_STATUS.PICKING]: [WAVE_STATUS.COMPLETED]
    }

    return validTransitions[fromStatus]?.includes(toStatus) || false
  }

  return {
    getStatusTagType,
    getStatusLabel,
    getStatusColor,
    formatTime,
    canExecuteAction,
    getNextStatus,
    isValidStatusTransition,
    WAVE_STATUS,
    WAVE_STATUS_OPTIONS
  }
}

/**
 * 波次搜索选项管理
 */
export function useWaveSearchOptions() {
  // 响应式数据状态
  const searchOptions = reactive({
    warehouse: [],
    picker: [],
    logisticsCarrier: [],
    logisticsChannel: [],
    status: WAVE_STATUS_OPTIONS // 静态数据直接使用
  })

  // 加载状态
  const loading = reactive({
    warehouse: false,
    picker: false,
    logisticsCarrier: false,
    logisticsChannel: false,
    all: false
  })

  // 错误状态
  const error = reactive({
    warehouse: null,
    picker: null,
    logisticsCarrier: null,
    logisticsChannel: null
  })

  // 获取物流承运商列表
  const fetchLogisticsCarrierOptions = async () => {
    if (loading.logisticsCarrier) return

    loading.logisticsCarrier = true
    error.logisticsCarrier = null

    try {
      const response = await options()
      if (response && response.code === 200) {
        searchOptions.logisticsCarrier = (response.data.carrierList || []).map((item) => ({
          value: item.value,
          label: item.en,
        }))
      }
    } catch (err) {
      error.logisticsCarrier = err.message || '获取承运商列表失败'
      console.error('获取承运商列表失败:', err)
    } finally {
      loading.logisticsCarrier = false
    }
  }

  // 获取物流渠道列表
  const fetchLogisticsChannelOptions = async () => {
    if (loading.logisticsChannel) return

    loading.logisticsChannel = true
    error.logisticsChannel = null

    try {
      const response = await channels()
      if (response && response.code === 200) {
        searchOptions.logisticsChannel = (response.data || []).map((item) => ({
          value: item.logisticsChannel,
          label: item.logisticsChannelName,
        }))
      }
    } catch (err) {
      error.logisticsChannel = err.message || '获取物流渠道列表失败'
      console.error('获取物流渠道列表失败:', err)
    } finally {
      loading.logisticsChannel = false
    }
  }

  // 批量获取所有动态选项
  const fetchAllOptions = async () => {
    loading.all = true

    try {
      await Promise.all([
        fetchLogisticsCarrierOptions(),
        fetchLogisticsChannelOptions()
      ])
    } finally {
      loading.all = false
    }
  }

  // 重置所有数据
  const resetOptions = () => {
    searchOptions.warehouse = []
    searchOptions.picker = []
    searchOptions.logisticsCarrier = []
    searchOptions.logisticsChannel = []
    Object.keys(error).forEach(key => {
      error[key] = null
    })
  }

  // 使用 toRefs 确保响应性
  const searchOptionsRefs = toRefs(searchOptions)

  return {
    // 数据（响应式）
    pickerOptions: searchOptionsRefs.picker,
    logisticsCarrierOptions: searchOptionsRefs.logisticsCarrier,
    logisticsChannelOptions: searchOptionsRefs.logisticsChannel,
    statusOptions: searchOptionsRefs.status,

    // 状态（只读）
    loading: readonly(loading),
    error: readonly(error),

    // 方法
    fetchLogisticsCarrierOptions,
    fetchLogisticsChannelOptions,
    fetchAllOptions,
    resetOptions
  }
}
