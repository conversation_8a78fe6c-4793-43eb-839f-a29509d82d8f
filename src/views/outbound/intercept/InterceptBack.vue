<script setup>
import { ref, reactive, onMounted } from 'vue'
import { backDetail, back, consumers } from '@/api/outbound/intercept'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Location } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useTabsStore } from '@/store/modules/tabs'
import SearchCellTab from './components/SearchCellTab.vue'

const router = useRouter()

const tabsStore = useTabsStore()

const interceptDetail = ref([])
const recommendSkuList = ref([])

const interceptNo = ref(null)
const customerCode = ref(null)
const whCode = ref(null)
const loading = ref(false)

const interceptInfo = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      interceptNo: interceptNo.value,
      customerCode: customerCode.value,
      whCode: whCode.value,
    }

    const response = await backDetail(params)

    if (response && response.data) {
      interceptDetail.value = response.data || []
      recommendSkuList.value = response.data.recommendSkuList || []

      if (recommendSkuList.value.length) {
        recommendSkuList.value.map((row, index) => {
          const item = { directQty: row.directQty, cellNo: row.cellNo }

          recommendSkuList.value[index].child = [item]
        })
      }

      console.log(recommendSkuList.value)
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

//复制
const copy = async (val) => {
  await navigator.clipboard.writeText(val)
  ElMessage.success('复制成功')
}

//回库上架 - 确认上架
const handleSub = async () => {
  try {
    // 构建查询参数
    const params = {
      interceptNo: interceptNo.value,
      customerCode: customerCode.value,
      whCode: whCode.value,
      operateType: 0,
      sourceNo: interceptDetail.value.sourceNo,
      tenantCode: interceptDetail.value.tenantCode,
      // skuList: interceptDetail.value.recommendSkuList,
    }

    const skuList = ref([])
    let overTotalQtyFlag = false
    let directQtyFlag = false
    recommendSkuList.value.map((sku) => {
      const item = {
        productSku: sku.productSku,
        productName: sku.productName,
        productQuality: sku.productQuality ? sku.productQuality : 0,
      }

      if (sku.child.length == 1) {
        if (!sku.child[0].directQty || sku.child[0].directQty == 0) {
          directQtyFlag = true
        }
        item['totalQty'] = sku.child[0].directQty
        item['cellNo'] = sku.child[0].cellNo
        item['directQty'] = sku.child[0].directQty
      } else {
        let totalQty = 0
        let cellNo = []
        let directQty = []

        sku.child.forEach((sv, inx) => {
          if (!sv.directQty || sv.directQty == 0) {
            directQtyFlag = true
          }
          totalQty += parseInt(sv.directQty)
          cellNo.push(sv.cellNo)
          directQty.push(parseInt(sv.directQty))
        })
        item['totalQty'] = totalQty
        item['cellNo'] = cellNo
        item['directQty'] = directQty
      }

      if (item.totalQty > sku.directQty) {
        overTotalQtyFlag = true
      }
      skuList.value.push(item)
    })

    console.log(skuList)
    console.log(params)
    params['skuList'] = skuList.value
    if (overTotalQtyFlag) {
      ElMessage.error('上架数量有误')
      return
    } else if (directQtyFlag) {
      ElMessage.error('数据填写不完整')
    } else {
      const response = await back(params)
      if (response && response.code == 200) {
        ElMessage.success('操作成功')
        closeTab()
        router.push('/outbound/intercept/listing')
      }
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  closeTab()
  router.go(-1)
}

const closeTab = () => {
  tabsStore.tabs.map((tab) => {
    if (tab.path == '/outbound/intercept/backDetail') {
      tabsStore.removeTab(tab.name)
    }
  })
}

const searchCellDialog = reactive({
  visible: false,
  title: '选择库位',
})

const selSkuRowIndex = ref(0) //SKU index
const selCellRowIndex = ref(0) //SKU - 库位 index
//添加行
const addRow = (skuIndex) => {
  const row = { directQty: '', cellNo: '' }
  recommendSkuList.value[skuIndex].child.push(row)
}
//移除行
const delRow = (skuIndex, cellRow) => {
  const index = recommendSkuList.value[skuIndex].child.indexOf(cellRow)
  if (index !== -1) {
    recommendSkuList.value[skuIndex].child.splice(index, 1)
  }
}
//选择库位 - 弹窗
const handelCellNo = (skuIndex, cellIndex) => {
  selSkuRowIndex.value = skuIndex
  selCellRowIndex.value = cellIndex
  searchCellDialog.visible = true
}
//选择库位 - check
const handleCheckCell = (row) => {
  let flag = false

  recommendSkuList.value[selSkuRowIndex.value].child.map((child, index) => {
    if (child.cellNo == row.cellNo) {
      flag = true
    }
  })

  if (flag) {
    ElMessage.error('不支持同一个产品回库到两个相同的库位上!')
  } else {
    recommendSkuList.value[selSkuRowIndex.value].child[selCellRowIndex.value].cellNo = row.cellNo
  }
  searchCellDialog.visible = false
}
//check 上架数量
const checkSkuQty = (skuInx, cellInx) => {
  const val = recommendSkuList.value[skuInx].child[cellInx].directQty
  const skuVal = recommendSkuList.value[skuInx].directQty

  if (val > skuVal || val == 0) {
    recommendSkuList.value[skuInx].child[cellInx].directQty = skuVal
  }
}

// 初始化
onMounted(() => {
  interceptNo.value = router.currentRoute.value.query.interceptNo
  customerCode.value = router.currentRoute.value.query.customerCode
  whCode.value = router.currentRoute.value.query.whCode

  interceptInfo()
})
</script>

<template>
  <div>
    <el-form label-width="100px" label-position="left">
      <el-card class="profile-card" shadow="hover" v-loading="loading">
        <el-row>
          <el-col :span="12">
            <span style="margin-left: 0px">
              <label style="font-size: 24px; font-weight: bold">{{
                interceptDetail.interceptNo
              }}</label>
              <el-link
                type="primary"
                style="margin-left: 5px; margin-bottom: 15px"
                :icon="DocumentCopy"
                @click="copy(interceptDetail.interceptNo)"
              >
              </el-link>
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="客户" prop="">
              <!-- <span>客户</span> -->
              <span class="type-name" v-if="interceptDetail.customerName">
                {{ interceptDetail.customerName }} ({{ interceptDetail.customerCode }})
              </span>
              <span class="type-name" v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物流渠道" prop="">
              <span class="type-name" v-if="interceptDetail.logisticsChannelName">
                {{ interceptDetail.logisticsChannelName }} ({{ interceptDetail.logisticsChannel }})
              </span>
              <span class="type-name" v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物流跟踪号" prop="">
              <span class="type-name" v-if="interceptDetail.expressNo">
                {{ interceptDetail.expressNo }}
              </span>
              <span class="type-name" v-else>-</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card style="margin-top: 10px">
        <div class="app-container">
          <span style="font-size: 20px; font-weight: bold; color: #0071e3">丨</span>
          <span style="font-size: 16px; font-weight: bolder">上架明细</span>
          <br />
          <el-table :data="recommendSkuList" style="width: 100%; margin-top: 20px">
            <el-table-column label="SKU" prop="productSku" width="" />
            <el-table-column label="产品明层" width="" prop="productName" />
            <el-table-column label="应上架数量" prop="directQty" width="" />
            <el-table-column label="实际上架数量" prop="directQty" width="">
              <template #default="scope">
                <el-input
                  v-for="(c, inx) in scope.row.child"
                  v-model="c.directQty"
                  style="margin-top: 3px"
                  @keyup="checkSkuQty(scope.$index, inx)"
                />
              </template>
            </el-table-column>
            <el-table-column label="库位" prop="cellNo" width="">
              <template #default="scope">
                <el-input-tag
                  :value="c.cellNo"
                  v-for="(c, inx) in scope.row.child"
                  readonly
                  style="margin-top: 3px"
                >
                  <template #suffix>
                    <el-icon @click="handelCellNo(scope.$index, inx)"><Location /></el-icon>
                  </template>
                </el-input-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" prop="" width="120px;" fixed="right">
              <template #default="scope">
                <span v-for="(c, inx) in scope.row.child">
                  <el-button v-if="inx == 0" @click="addRow(scope.$index)" type="primary"
                    >添加</el-button
                  >
                  <el-button
                    v-else
                    type="danger"
                    @click="delRow(scope.$index, c)"
                    style="margin-top: 3px"
                    >移除</el-button
                  >
                  <br />
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-form>

    <div class="footer">
      <el-button style="width: 85px; height: 45px" @click="handleBack">取消</el-button>
      <el-button style="width: 85px; height: 45px" type="primary" @click="handleSub"
        >确认上架</el-button
      >
    </div>

    <!-- 选择库位 TAB -->
    <SearchCellTab
      :visible="searchCellDialog.visible"
      :title="searchCellDialog.title"
      :whCode="whCode"
      @checkCell="handleCheckCell"
    />
  </div>
</template>

<style>
.form-item-text-align {
  text-align: left;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-left: 0px;
  text-align: center;
}
</style>
