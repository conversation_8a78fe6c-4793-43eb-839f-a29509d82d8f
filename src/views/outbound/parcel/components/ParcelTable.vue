<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { DocumentCopy, WarningFilled } from '@element-plus/icons-vue'

// 路由相关
const router = useRouter()

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  outboundList: {
    type: Array,
    required: true,
  },
  total: {
    type: Number,
    default: 0,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  statusType: {
    type: String,
    default: '',
  },
  waveSplicingVal: {
    type: String,
    default: '0',
  },
})

const emit = defineEmits([
  'pagination-change',
  'selection-change',
  'make-exception',
  'print-shipment',
  'print-express',
  'create-wave',
  'quick-out',
])

const splicingOrder = ref(false)

// 表格选中行
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection

  // emit('selection-change', selection)
}

//生成波次
const handleCreateWave = () => {
  if (selectedRows.value.length == 0) {
    ElMessage.error('请选择要操作的单据')
    return
  }

  emit('create-wave', selectedRows.value)
}

//快捷出库
const handleQuickOut = () => {
  if (selectedRows.value.length == 0) {
    ElMessage.error('请选择要操作的单据')
    return
  }

  emit('quick-out', selectedRows.value)
}

//打印面单
const handlePrintOrder = () => {
  if (selectedRows.value.length == 0) {
    ElMessage.error('请选择要打印的单据')
    return
  }

  emit('print-express', selectedRows.value)
}

// 处理页码或每页条数变化
const handleSizeChange = (size) => {
  emit('pagination-change', { page: props.currentPage, size })
}

const handleCurrentChange = (page) => {
  emit('pagination-change', { page, size: props.pageSize })
}

// 标记异常
const handleMarkException = (row) => {
  emit('make-exception', row)
}

//打印发货清单
const printShipment = (row) => {
  emit('print-shipment', row)
}

//复制
const handleCopy = async (val) => {
  await navigator.clipboard.writeText(val)
  ElMessage.success('复制成功')
}

//分拨后立即拼接面单
const waveSplicingChange = async (val) => {
  emit('wave-splicing', val)
}

const handleExpressLink = async (expressNo) => {
  window.open('https://www.track123.com/cn/track?trackNos=' + expressNo, '_blank')
}

const handleDetail = (row) => {
  router.push({
    path: '/outbound/detail',
    query: {
      deliveryNo: row.deliveryNo,
      customerCode: row.customerCode,
      whCode: row.whCode,
      sourceNo: row.sourceNo,
    },
  })
}
</script>

<template>
  <div>
    <el-button type="primary" v-if="statusType == 10" @click="handleCreateWave">
      生成波次
    </el-button>
    <el-button type="primary" v-if="statusType == 20" @click="handleQuickOut"> 快捷出库 </el-button>
    <el-button v-if="statusType != '' && statusType != 99" @click="handlePrintOrder">
      打印面单
    </el-button>

    <el-checkbox
      disabled
      v-if="statusType == 10"
      style="margin-left: 20px"
      v-model="props.waveSplicingVal"
      @change="waveSplicingChange"
    >
      分拨后立即拼接面单
    </el-checkbox>
  </div>
  <el-card class="table-card" shadow="hover" style="margin-top: 10px">
    <template #header>
      <div class="card-header">
        <!--        <span>用户列表</span>-->
      </div>
    </template>

    <div v-if="outboundList.length === 0" class="empty-data">
      <p>暂无数据</p>
    </div>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="outboundList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="出库单号" prop="sourceNo" width="190" fixed="left">
        <template #default="scope">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="客户已发起截单，请前往截单模块拦截此单据"
            placement="top-start"
          >
            <el-icon v-if="scope.row.interceptFlag == 1" style="color: #e78c3a"
              ><WarningFilled
            /></el-icon>
          </el-tooltip>
          <el-link type="primary" v-if="scope.row.sourceNo" @click="() => handleDetail(scope.row)">
            {{ scope.row.sourceNo }}
          </el-link>
          <el-link
            type="primary"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.sourceNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" />
      <el-table-column label="产品数量" prop="skuCount" width="100px;" align="center" />
      <el-table-column label="SKU * 数量" prop="" width="200px;" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.productList.length < 2" v-for="pro in scope.row.productList"
            >{{ pro.productSku }} * {{ pro.qty }}
          </span>
          <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
            <template #default>
              <el-table :data="scope.row.productList">
                <el-table-column label="SKU" prop="productSku" width="150" />
                <el-table-column label="每箱数量" prop="qty" width="100" />
              </el-table>
            </template>
            <template #reference>
              <el-tag>多个({{ scope.row.productList.length }})</el-tag>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" prop="" show-overflow-tooltip width="150px;" align="center">
        <template #default="scope">
          <span v-if="scope.row.productList.length < 2" v-for="pro in scope.row.productList"
            >{{ pro.productName }}
          </span>
          <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
            <template #default>
              <el-table :data="scope.row.productList">
                <el-table-column label="SKU" prop="productSku" width="150" />
                <el-table-column label="产品名称" prop="productName" width="150" />
              </el-table>
            </template>
            <template #reference>
              <el-tag>多个({{ scope.row.productList.length }})</el-tag>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="产品分类" prop="" width="150px;" align="center">
        <template #default="scope">
          <span v-if="scope.row.productList.length < 2" v-for="pro in scope.row.productList"
            >{{ pro.fnsku ? pro.fnsku : '-' }}
          </span>
          <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
            <template #default>
              <el-table :data="scope.row.productList">
                <el-table-column label="SKU" prop="productSku" width="150" />
                <el-table-column label="产品分类" prop="" width="150">
                  <template #default="sku">
                    <span>{{ sku.row.fnsku ? sku.row.fnsku : '-' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
            <template #reference>
              <el-tag>多个({{ scope.row.productList.length }})</el-tag>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="波次号" prop="waveNo" align="center" width="150px;">
        <template #default="scope">
          {{ scope.row.waveNo }}
          <el-link
            type="primary"
            v-if="scope.row.waveNo"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.waveNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="面单打印" prop="expressPrintStatusName" width="100" align="center" />
      <el-table-column label="客户" align="center" width="200px">
        <template #default="scope">
          {{ scope.row.customerName }} ({{ scope.row.customerCode }})
        </template>
      </el-table-column>
      <el-table-column label="订单品种类型" prop="varietyTypeName" width="120px;" align="center" />
      <el-table-column label="物流渠道" prop="logisticsChannelName" width="180px;" align="center" />
      <el-table-column label="国家" prop="countryRegionName" width="80px;" align="center" />
      <el-table-column
        label="物流跟踪号"
        prop="expressNo"
        width="230px;"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          <el-link
            type="primary"
            v-if="scope.row.expressNo"
            @click="handleExpressLink(scope.row.expressNo)"
          >
            {{ scope.row.expressNo }}
          </el-link>
          <el-link
            type="primary"
            v-if="scope.row.expressNo"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.expressNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="包裹数量" prop="packageNum" width="80px;" align="center" />
      <el-table-column
        label="参考单号"
        prop="referOrderNo"
        width="200px;"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.referOrderNo }}
          <el-link
            type="primary"
            v-if="scope.row.referOrderNo"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.referOrderNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="销售平台" prop="salesPlatformName" width="150px;" align="center" />
      <el-table-column
        label="平台单号"
        prop="platformOrderNo"
        width="230px;"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.platformOrderNo }}
          <el-link
            type="primary"
            v-if="scope.row.platformOrderNo"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.platformOrderNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <!--      <el-table-column label="关联调拨单" prop="" width="180px;" align="center" />-->
      <el-table-column label="平台模式" prop="platformTypeName" width="180px;" align="center">
        <template #default="scope">
          {{ scope.row.platformTypeName ? scope.row.platformTypeName : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="订单来源" prop="orderSourceName" width="180px;" align="center" />
      <el-table-column label="创建时间" prop="orderCreateTime" width="180px;" align="center" />
      <el-table-column label="生成波次时间" prop="createWaveTime" width="180px;" align="center">
        <template #default="scope">
          {{ scope.row.createWaveTime ? scope.row.createWaveTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="拣货时间" prop="pickTime" width="180px;" align="center">
        <template #default="scope">
          {{ scope.row.pickTime ? scope.row.pickTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="复核时间" prop="recheckTime" width="180px;" align="center">
        <template #default="scope">
          {{ scope.row.recheckTime ? scope.row.recheckTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="称重时间" prop="weighTime" width="180px;" align="center">
        <template #default="scope">
          {{ scope.row.weighTime ? scope.row.weighTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="出库时间" prop="outboundTime" width="180px;" align="center">
        <template #default="scope">
          {{ scope.row.outboundTime ? scope.row.outboundTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="当前销售代表"
        prop="currentSalesPeopleName"
        width="120px;"
        align="center"
      >
        <template #default="scope">
          {{ scope.row.currentSalesPeopleName ? scope.row.currentSalesPeopleName : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="当前客服代表" prop="" width="120px;" align="center" />
      <el-table-column
        label="状态"
        prop="statusName"
        fixed="right"
        width="90px;"
        v-if="statusType == ''"
        align="center"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.status == 10" type="warning">{{ scope.row.statusName }}</el-tag>
          <el-tag v-else-if="scope.row.status == 100" type="success">{{
            scope.row.statusName
          }}</el-tag>
          <el-tag
            v-else-if="scope.row.status == 20"
            style="color: #c13176; border-color: #c13176"
            color="#f7eafb"
            >{{ scope.row.statusName }}</el-tag
          >
          <el-tag
            v-else-if="scope.row.status == 30"
            style="color: #b32fd4; border-color: #e3b1ef"
            color="#f7eafb"
            >{{ scope.row.statusName }}</el-tag
          >
          <el-tag v-else-if="scope.row.status == 99" type="info">{{ scope.row.statusName }}</el-tag>
          <el-tag v-else-if="scope.row.status == 15" type="danger">{{
            scope.row.statusName
          }}</el-tag>
          <el-tag v-else>{{ scope.row.statusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="200"
        v-if="statusType != '99' && statusType != 99"
        align="center"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            style="color: #123ef5"
            @click="printShipment(scope.row)"
            v-if="scope.row.status !== 99"
          >
            打印发货清单
          </el-button>

          <el-button
            link
            type="primary"
            style="color: #123ef5"
            @click="() => handleMarkException(scope.row)"
            v-if="scope.row.status == 20 || scope.row.status == 30"
          >
            标记异常
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<style scoped>
.table-card {
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
