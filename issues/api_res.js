// allWhUser 接口返回
// 请求参数
/**
 * {
    "whCode": "CA"
}
 */
api_res = {
    "code": 200,
    "data": [
        "SZJTGYL",
        "libra",
        "jtadmin",
        "jtca5",
        "jtcici",
        "chunjie",
        "jtca3",
        "jtca1",
        "ZRR001",
        "CRX001",
        "lao001",
        "jtjinjie",
        "ZMX001",
        "XWZ001",
        "jt_ca_auto",
        "ZKM001",
        "gtdev",
        "Renshi",
        "LSH",
        "Wangzhiwei",
        "MN001"
    ],
    "msg": "操作成功"
}


// saveOrUpDatePicker 接口返回
// 请求参数
/**
 * {
    "whCode": "CA",
    "waveNoList": [
        "W250729AXO"
    ],
    "picker": "gtdev"
}
 */
api_res = {
    "code": 200,
    "data": {},
    "msg": "操作成功"
}

// pickSkuList 接口返回
// 请求参数
/**
 * {
    "whCode": "CA",
    "waveNo": "W250729AXO"
}
 */
api_res = {
    "code": 200,
    "data": {
        "whCode": "CA",
        "waveNo": "W250729AXO",
        "deliveryNo": "",
        "sourceNo": "",
        "customerCode": "5252001",
        "customerName": "",
        "orderCount": 1,
        "totalCount": 2,
        "pickingType": 2,
        "orderType": 1,
        "outboundWay": 1,
        "remark": "",
        "skuList": [
            {
                "productSku": "demo",
                "barcode": "dddddd123",
                "fnsku": "",
                "customerCode": "5252001",
                "productName": "demo",
                "productQuality": 0,
                "planQty": 1,
                "pickQty": 0,
                "reCommendCellList": [
                    {
                        "customerCode": "5252001",
                        "item": "demo",
                        "cellNo": "TMP",
                        "usefullyQty": 32,
                        "qty": 1
                    }
                ]
            },
            {
                "productSku": "demo2",
                "barcode": "demo2",
                "fnsku": "",
                "customerCode": "5252001",
                "productName": "demo2",
                "productQuality": 0,
                "planQty": 1,
                "pickQty": 0,
                "reCommendCellList": [
                    {
                        "customerCode": "5252001",
                        "item": "demo2",
                        "cellNo": "TMP",
                        "usefullyQty": 56,
                        "qty": 1
                    }
                ]
            }
        ],
        "boxList": []
    },
    "msg": "操作成功"
}


// pcPick 接口返回
// 请求参数
/**
 * {
    "deliveryNo": "",
    "waveNo": "W250729AXO",
    "whCode": "CA",
    "skuList": [
        {
            "id": "demo05252001",
            "productSku": "demo",
            "cellNo": "TMP",
            "pickQty": 1,
            "pickQty1": 1,
            "productQuality": 0,
            "planQty": 1,
            "customerCode": "5252001"
        }
    ]
}
 */
api_res = {
    "code": 200,
    "data": {},
    "msg": "操作成功"
}
