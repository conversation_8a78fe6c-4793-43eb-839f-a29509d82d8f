<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DocumentCopy, Plus, Delete, Setting } from '@element-plus/icons-vue'
import { getWaveDetail, getWaveSkuList } from '@/api/outbound/wave'
import { selectCell } from '@/api/outbound/intercept'

// 路由
const route = useRoute()
const router = useRouter()

// 波次号
const waveNo = ref(route.query.waveNo || '')

// 页面加载状态
const loading = ref(false)

// 波次基本信息
const waveDetail = ref({
  waveNo: '',
  status: '',
  statusName: '',
  orderCount: 0,
  planQty: 0,
  pickQty: 0,
  assigntor: '',
  createTime: '',
  pickingTypeName: ''
})

// 产品列表
const productList = ref([])
const selectedProducts = ref([])

// 库位设置
const locationSettings = ref([
  {
    id: 1,
    cellNo: 'A01-01-01',
    areaType: 1,
    areaTypeName: '拣选区',
    cellType: 1,
    cellTypeName: '货架'
  }
])

// 库位选择对话框
const locationDialog = ref({
  visible: false,
  title: '选择库位'
})

// 库位列表
const cellList = ref([])
const cellLoading = ref(false)

// 获取波次详情
const fetchWaveDetail = async () => {
  if (!waveNo.value) {
    ElMessage.error('波次号不能为空')
    return
  }

  loading.value = true

  try {
    const params = {
      waveNo: waveNo.value,
      whCode: 'CA'
    }

    const response = await getWaveDetail(params)

    if (response && response.code === 200) {
      waveDetail.value = response.data || {}
    }
  } catch (error) {
    console.error('获取波次详情失败', error)
    ElMessage.error('获取波次详情失败')
  } finally {
    loading.value = false
  }
}

// 获取产品列表
const fetchProductList = async () => {
  if (!waveNo.value) return

  loading.value = true

  try {
    const params = {
      waveNo: waveNo.value,
      whCode: 'CA',
      current: 1,
      size: 1000 // 获取所有产品
    }

    const response = await getWaveSkuList(params)

    if (response && response.code === 200) {
      // 为每个产品添加拣货数量字段
      productList.value = (response.data.records || []).map(item => ({
        ...item,
        pickingQty: 0, // 拣货数量
        remainQty: item.planQty - item.pickQty, // 剩余数量
        selected: false
      }))
    }
  } catch (error) {
    console.error('获取产品列表失败', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

// 复制波次号
const copyWaveNo = async () => {
  try {
    await navigator.clipboard.writeText(waveDetail.value.waveNo)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败', error)
    ElMessage.error('复制失败')
  }
}

// 处理产品选择
const handleProductSelection = (selection) => {
  selectedProducts.value = selection
}

// 处理拣货数量变化
const handlePickingQtyChange = (row, value) => {
  if (value < 0) {
    row.pickingQty = 0
    ElMessage.warning('拣货数量不能小于0')
    return
  }
  
  if (value > row.remainQty) {
    row.pickingQty = row.remainQty
    ElMessage.warning('拣货数量不能大于剩余数量')
    return
  }
  
  row.pickingQty = value
}

// 添加库位
const addLocation = () => {
  locationDialog.value.visible = true
  fetchCellList()
}

// 删除库位
const removeLocation = (index) => {
  locationSettings.value.splice(index, 1)
}

// 获取库位列表
const fetchCellList = async () => {
  cellLoading.value = true

  try {
    const params = {
      whCode: 'CA',
      current: 1,
      size: 100
    }

    const response = await selectCell(params)

    if (response && response.code === 200) {
      cellList.value = response.data.records || []
    }
  } catch (error) {
    console.error('获取库位列表失败', error)
    ElMessage.error('获取库位列表失败')
  } finally {
    cellLoading.value = false
  }
}

// 选择库位
const selectLocation = (cell) => {
  const exists = locationSettings.value.find(item => item.cellNo === cell.cellNo)
  if (exists) {
    ElMessage.warning('该库位已存在')
    return
  }

  locationSettings.value.push({
    id: Date.now(),
    cellNo: cell.cellNo,
    areaType: cell.areaType,
    areaTypeName: cell.areaTypeName,
    cellType: cell.cellType,
    cellTypeName: cell.cellTypeName
  })

  locationDialog.value.visible = false
  ElMessage.success('库位添加成功')
}

// 确认拣货
const confirmPicking = async () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请选择要拣货的产品')
    return
  }

  const hasPickingQty = selectedProducts.value.some(item => item.pickingQty > 0)
  if (!hasPickingQty) {
    ElMessage.warning('请填写拣货数量')
    return
  }

  if (locationSettings.value.length === 0) {
    ElMessage.warning('请设置拣货库位')
    return
  }

  try {
    await ElMessageBox.confirm('确认提交拣货信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // TODO: 调用拣货API
    ElMessage.success('拣货信息提交成功')
    
    // 返回波次列表
    router.push('/outbound/wave')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交拣货信息失败', error)
      ElMessage.error('提交拣货信息失败')
    }
  }
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 页面初始化
onMounted(() => {
  if (waveNo.value) {
    fetchWaveDetail()
    fetchProductList()
  }
})

// 计算总拣货数量
const totalPickingQty = computed(() => {
  return selectedProducts.value.reduce((sum, item) => sum + (item.pickingQty || 0), 0)
})
</script>

<template>
  <div class="wave-picking-container" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>波次拣货</h2>
        <div class="wave-info">
          <span class="wave-no">
            {{ waveDetail.waveNo }}
            <el-button 
              :icon="DocumentCopy" 
              size="small" 
              text 
              @click="copyWaveNo"
              title="复制波次号"
            />
          </span>
          <el-tag :type="waveDetail.status === 20 ? 'primary' : 'warning'">
            {{ waveDetail.statusName }}
          </el-tag>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="confirmPicking">确认拣货</el-button>
      </div>
    </div>

    <!-- 波次基本信息 -->
    <el-card class="wave-info-card" shadow="hover">
      <template #header>
        <span>波次信息</span>
      </template>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">订单数量：</span>
          <span class="value">{{ waveDetail.orderCount }}</span>
        </div>
        <div class="info-item">
          <span class="label">应拣数量：</span>
          <span class="value">{{ waveDetail.planQty }}</span>
        </div>
        <div class="info-item">
          <span class="label">已拣数量：</span>
          <span class="value">{{ waveDetail.pickQty }}</span>
        </div>
        <div class="info-item">
          <span class="label">拣货员：</span>
          <span class="value">{{ waveDetail.assigntor || '未分配' }}</span>
        </div>
        <div class="info-item">
          <span class="label">波次类型：</span>
          <span class="value">{{ waveDetail.pickingTypeName }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间：</span>
          <span class="value">{{ waveDetail.createTime }}</span>
        </div>
      </div>
    </el-card>

    <!-- 库位设置 -->
    <el-card class="location-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>拣货库位</span>
          <el-button :icon="Plus" size="small" @click="addLocation">添加库位</el-button>
        </div>
      </template>
      <div class="location-list">
        <div
          v-for="(location, index) in locationSettings"
          :key="location.id"
          class="location-item"
        >
          <div class="location-info">
            <span class="cell-no">{{ location.cellNo }}</span>
            <span class="cell-type">{{ location.areaTypeName }} - {{ location.cellTypeName }}</span>
          </div>
          <el-button
            :icon="Delete"
            size="small"
            text
            type="danger"
            @click="removeLocation(index)"
          />
        </div>
        <div v-if="locationSettings.length === 0" class="empty-location">
          <span>暂无库位设置，请添加拣货库位</span>
        </div>
      </div>
    </el-card>

    <!-- 产品列表 -->
    <el-card class="product-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>产品列表</span>
          <div class="summary-info">
            <span>已选择：{{ selectedProducts.length }} 个产品</span>
            <span>拣货数量：{{ totalPickingQty }}</span>
          </div>
        </div>
      </template>

      <el-table
        :data="productList"
        @selection-change="handleProductSelection"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="productSku" label="SKU" width="150" />

        <el-table-column prop="productSkuName" label="产品名称" show-overflow-tooltip />

        <el-table-column prop="planQty" label="应拣数量" width="100" align="center" />

        <el-table-column prop="pickQty" label="已拣数量" width="100" align="center" />

        <el-table-column prop="remainQty" label="剩余数量" width="100" align="center">
          <template #default="scope">
            <span :class="{ 'text-danger': scope.row.remainQty <= 0 }">
              {{ scope.row.remainQty }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="拣货数量" width="150" align="center">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.pickingQty"
              :min="0"
              :max="scope.row.remainQty"
              size="small"
              @change="(value) => handlePickingQtyChange(scope.row, value)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="stockQualityName" label="库位属性" width="120" />
      </el-table>
    </el-card>

    <!-- 库位选择对话框 -->
    <el-dialog
      v-model="locationDialog.visible"
      :title="locationDialog.title"
      width="800px"
      destroy-on-close
    >
      <el-table
        :data="cellList"
        :loading="cellLoading"
        stripe
        style="width: 100%"
        max-height="400px"
      >
        <el-table-column prop="cellNo" label="库位编码" width="150" />

        <el-table-column prop="areaNo" label="库区编码" width="120" />

        <el-table-column prop="areaTypeName" label="库区类型" width="120" />

        <el-table-column prop="cellTypeName" label="库位类型" width="120" />

        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="selectLocation(scope.row)"
            >
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<style scoped>
.wave-picking-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 120px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.wave-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.wave-no {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.wave-info-card,
.location-card,
.product-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  margin-right: 8px;
}

.info-item .value {
  color: var(--el-text-color-primary);
}

.location-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.location-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  min-width: 200px;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.cell-no {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.cell-type {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.empty-location {
  padding: 20px;
  text-align: center;
  color: var(--el-text-color-placeholder);
  background-color: var(--el-bg-color-page);
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  width: 100%;
}

.text-danger {
  color: var(--el-color-danger);
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-card__header) {
  background-color: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-lighter);
}
</style>
