import request from '@/utils/request'

/**
 * 截单列表
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function listing(data) {
  return request({
    url: '/outbound/intercept/listing',
    method: 'post',
    data,
  })
}

/**
 * 查询参数
 * @returns {Promise} options
 */
export function options() {
  return request({
    url: '/outbound/intercept/options',
    method: 'post',
  })
}

/**
 * 分组状态数量统计
 * @returns {Promise} list
 */
export function statusSum(data) {
  return request({
    url: '/outbound/intercept/statusSum',
    method: 'post',
    data,
  })
}

/**
 * 客户列表
 * @returns {Promise} consumerList
 */
export function consumers(data) {
  return request({
    url: '/outbound/consumers',
    method: 'post',
    data,
  })
}

/**
 * 渠道列表
 * @returns {Promise} channelList
 */
export function channels(data) {
  return request({
    url: '/outbound/channels',
    method: 'post',
    data,
  })
}

/**
 * 截单 - 回库上架详情
 * @returns {Promise} list
 */
export function backDetail(data) {
  return request({
    url: '/outbound/intercept/backDetail',
    method: 'post',
    data,
  })
}

/**
 * 截单 - 回库上架
 * @returns {Promise} list
 */
export function back(data) {
  return request({
    url: '/outbound/intercept/confirmBack',
    method: 'post',
    data,
  })
}

/**
 * 截单 - 回库上架 - 选择库位
 * @returns {Promise} list
 */
export function selectCell(data) {
  return request({
    url: '/outbound/intercept/selectCell',
    method: 'post',
    data,
  })
}

//库区类型
export const AREATYPE_TEXT = {
  1: '拣选区',
  2: '存储区',
  3: '残次品区',
  4: '暂存区',
  5: '其他区',
}
//库位类型
export const CELLTYPE_TEXT = {
  1: '货架',
  2: '地推',
}
//库位是否为空
export const NULLCELL_TEXT = {
  0: '否',
  1: '是',
}
