<script setup>
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue'
import { Setting } from '@element-plus/icons-vue'
import { getLodop, installLodop } from '@/utils/lodop/lodop'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  barcodeDisabled: {
    type: Boolean,
    default: true,
  },
  orderNoDisabled: {
    type: Boolean,
    default: false,
  },
  initialWaveNo: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['scan', 'setting', 'weight-finish', 'reset'])
const orderNo = ref('')
const barcode = ref('')
const weight = ref('')

// 打印机相关
const printerList = ref([])
const selectedPrinter = ref('')
const printLoading = ref(false)
const lodopChecking = ref(true) // 添加检测状态

const base64Pdf = ref(null)
// SCAN
const handleScan = async () => {
  emit('scan', orderNo.value)
  // if (!lodop.value) {
  //   console.error('Lodop 尚未准备好!')
  //   return
  // }
  // const file = 'http://localhost:5173/a.pdf'
  // const response = await fetch(file)
  // const pdfBlob = await response.blob()
  // base64Pdf.value = await new Promise((resolve) => {
  //   const reader = new FileReader()
  //   reader.onload = () => resolve(reader.result)
  //   reader.readAsDataURL(pdfBlob)
  // })
  // console.log(base64Pdf.value)
  // lodop.value.PRINT_INIT('pdf打印')
  // lodop.value.SET_LICENSES('', '', '', '')
  // lodop.value.ADD_PRINT_PDF(0, 0, '100%', '100%', base64Pdf.value.split(',')[1])
  // // lodop.value.PREVIEW()
  // lodop.value.PRINT()
}

//SETTING
const showSetting = () => {
  emit('setting')
}

const handleWeightFinish = () => {
  emit('weight-finish', orderNo.value, weight.value)
}

const handleWeightReset = () => {
  orderNo.value = ''
  weight.value = ''
}

const handleReset = () => {
  orderNo.value = ''
  barcode.value = ''
  emit('reset')
}

const lodop = ref(null)
const lodopError = ref('')

// 获取打印机列表
function getPrinterList() {
  if (!lodop.value) {
    console.error('Lodop 尚未准备好!')
    return
  }

  try {
    const printerCount = lodop.value.GET_PRINTER_COUNT()
    const printers = []

    for (let i = 0; i < printerCount; i++) {
      const printerName = lodop.value.GET_PRINTER_NAME(i)
      printers.push({
        index: i,
        name: printerName,
        value: i.toString(),
      })
    }

    printerList.value = printers

    // 选中默认打印机
    if (printers.length > 0) {
      const defaultPrintName = lodop.value.GET_PRINTER_NAME(-1)
      const defaultPrinter = printers.find((printer) => printer.name === defaultPrintName)
      if (defaultPrinter) {
        selectedPrinter.value = defaultPrinter.value
      }
    }
  } catch (error) {
    console.error('获取打印机列表失败:', error)
  }
}

// 处理打印机选择变更
function handlePrinterChange(value) {
  if (!lodop.value) {
    console.error('Lodop 尚未准备好!')
    return
  }

  try {
    const printerIndex = parseInt(value)
    lodop.value.SET_PRINTER_INDEXA(printerIndex)
    console.log('已设置打印机:', lodop.value.GET_PRINTER_NAME(printerIndex))
  } catch (error) {
    console.error('设置打印机失败:', error)
  }
}

// 监听 initialWaveNo 的变化
watch(
  () => props.initialWaveNo,
  (newWaveNo) => {
    if (newWaveNo && !orderNo.value) {
      orderNo.value = newWaveNo
    }
  },
  { immediate: true }
)

onMounted(async () => {
  installLodop()
  setTimeout(() => {
    const result = getLodop()
    lodopChecking.value = false // 检测完成
    if (result.error) {
      lodopError.value = result.error
      console.error(result.error)
      printLoading.value = false
      return
    }
    lodop.value = result.lodop
    printLoading.value = true
    // lodop初始化完成后获取打印机列表
    getPrinterList()
  }, 500)
})
</script>

<template>
  <el-card class="scan-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span style="font-size: 24px">二次分拣</span>
      </div>
    </template>

    <div class="scan-area">
      <!-- 提示信息 -->
      <el-alert v-if="lodopChecking" type="info" style="margin-bottom: 20px">
        <template #default>
          <span style="display: inline-flex; align-items: center"> 正在检测Lodop控件... </span>
        </template>
      </el-alert>
      <el-alert v-else :type="printLoading ? 'success' : 'warning'" style="margin-bottom: 20px">
        <template #default>
          <span style="display: inline-flex; align-items: center">
            {{ printLoading ? 'Lodop打印' : '未检测到Lodop控件' }}
            <el-link type="primary" style="margin-left: 5px">
              {{ printLoading ? '设置' : '点击安装' }}
            </el-link>
          </span>
        </template>
      </el-alert>

      <el-row style="font-weight: bold"> 波次单号 </el-row>
      <el-row>
        <el-input
          v-model="orderNo"
          :disabled="orderNoDisabled"
          style="width: 280px; height: 40px; margin-top: 10px"
          placeholder="请扫描"
          clearable
          @keyup.enter="handleScan"
        />
      </el-row>
      <el-row style="font-weight: bold; margin-top: 20px"> Barcode </el-row>
      <el-row>
        <el-input
          v-model="barcode"
          :disabled="barcodeDisabled"
          style="width: 280px; height: 40px; margin-top: 10px"
          placeholder="请扫描"
          clearable
          @keyup.enter=""
        />
      </el-row>

      <el-row v-if="orderNoDisabled" style="margin-top: 20px">
        <el-button
          type="warning"
          style="width: 280px; height: 40px"
          @click="handleReset"
        >
          清空重做
        </el-button>
      </el-row>

      <div class="footer">
        <el-select
          v-model="selectedPrinter"
          placeholder="请选择打印机"
          style="width: 200px"
          @change="handlePrinterChange"
        >
          <el-option
            v-for="printer in printerList"
            :key="printer.value"
            :value="printer.value"
            :label="printer.name"
          >
            {{ printer.name }}
          </el-option>
        </el-select>
        <el-link style="margin-left: 10px">
          <el-icon @click="showSetting">
            <Setting />
          </el-icon>
        </el-link>
      </div>
    </div>
  </el-card>
</template>

<style scoped>
.scan-card {
  border-radius: 8px;
  height: calc(100vh - 120px);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.scan-area {
  position: relative;
  height: 70vh;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-left: 0px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 500;
}
</style>
