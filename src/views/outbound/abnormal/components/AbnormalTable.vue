<template>
  <el-card class="table-card" shadow="hover">
    <el-table
      ref="abnormalTable"
      v-loading="loading"
      :data="abnormalList"
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <el-table-column type="selection" width="55" fixed="left" />

      <!-- 基础信息列 -->
      <el-table-column
        v-for="column in tableColumns"
        :key="column.prop"
        :prop="column.customRender ? undefined : column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :fixed="column.fixed"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template #default="{ row }">
          <!-- 出库单号显示 -->
          <template v-if="column.prop === 'sourceNo'">
            <span>{{ row.sourceNo }}</span>
            <el-icon class="copy-icon" @click="copyToClipboard(row.sourceNo)" title="复制出库单号">
              <CopyDocument />
            </el-icon>
          </template>

          <!-- 产品列表显示 -->
          <template v-else-if="column.prop === 'productList'">
            <div v-if="row.productList && row.productList.length > 0">
              <el-text v-if="row.productList.length === 1" type="primary" size="small">
                {{ row.productList[0].productSku }} * {{ row.productList[0].qty }}
              </el-text>
              <el-tooltip v-else placement="top" effect="dark">
                <template #content>
                  <div v-for="(product, index) in row.productList" :key="index">
                    {{ product.productSku }} * {{ product.qty }}
                  </div>
                </template>
                <el-text type="primary" size="small">多个 ({{ row.productList.length }})</el-text>
              </el-tooltip>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 客户显示 -->
          <template v-else-if="column.prop === 'customerName'">
            {{ row.customerName + '(' + row.customerCode + ')' }}
          </template>

          <!-- 物流渠道显示 -->
          <template v-else-if="column.prop === 'logisticsChannelName'">
            {{ row.logisticsChannelName + '(' + row.logisticsChannel + ')' }}
          </template>

          <!-- 订单类型显示 -->
          <template v-else-if="column.prop === 'outboundType'">
            {{ getOrderTypeText(row.outboundType) }}
          </template>

          <!-- 普通字段显示 -->
          <template v-else>
            {{ row[column.prop] || '-' }}
          </template>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            v-for="action in getVisibleRowActions(row)"
            :key="action.key"
            :type="action.type"
            @click="handleRowAction(action, row)"
            link
          >
            {{ action.label }}
          </el-button>

          <!-- 更多操作下拉菜单 -->
          <el-dropdown
            v-if="getMoreActions(row).length > 0"
            @command="(command) => handleRowAction(command, row)"
          >
            <el-button type="primary" link>
              更多
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="action in getMoreActions(row)"
                  :key="action.key"
                  :command="action.key"
                >
                  {{ action.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script>
import { MoreFilled, CopyDocument } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { removeEx, confirmEx } from '@/api/outbound/abnormal'

export default {
  name: 'AbnormalTable',
  components: {
    MoreFilled,
    CopyDocument,
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    abnormalList: {
      type: Array,
      required: true,
    },
    total: {
      type: Number,
      default: 0,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 20,
    },
  },
  emits: ['selection-change', 'pagination-change', 'row-action', 'refresh'],
  data() {
    return {
      // 表格列配置
      tableColumns: [
        { prop: 'sourceNo', label: '出库单号', width: 180, fixed: 'left', customRender: true },
        { prop: 'platformOrderNo', label: '平台单号', width: 160 },
        { prop: 'waveNo', label: '波次号', width: 140 },
        { prop: 'productList', label: 'SKU * 数量', width: 180, customRender: true },
        { prop: 'customerName', label: '客户', width: 180 },
        { prop: 'outboundType', label: '订单类型', width: 120, customRender: true },
        { prop: 'logisticsChannelName', label: '物流渠道', width: 160 },
        { prop: 'expressNo', label: '物流跟踪号', width: 180 },
        { prop: 'exceptionTime', label: '异常时间', width: 160, customRender: true },
      ],
      // 行操作按钮配置
      rowActions: [
        {
          key: 'remove_exception',
          label: '移除异常',
          type: 'primary',
          visible: () => true,
        },
        {
          key: 'enter_exception',
          label: '确认异常',
          type: 'primary',
          visible: () => true,
        },
      ],
      // 出库类型文本映射
      outboundTypeTextMap: {
        1: '一件代发',
        2: '备货中转',
      },
    }
  },
  methods: {
    // 复制到剪贴板
    async copyToClipboard(text) {
      await navigator.clipboard.writeText(text)
      ElMessage.success('复制成功')
    },

    // 获取订单类型文本
    getOrderTypeText(outboundType) {
      return this.outboundTypeTextMap[outboundType] || '-'
    },

    // 获取可见的行操作按钮（前两个）
    getVisibleRowActions(row) {
      const visibleActions = this.rowActions.filter((action) => action.visible(row))
      return visibleActions.slice(0, 2)
    },

    // 获取更多操作按钮（第三个及以后）
    getMoreActions(row) {
      const visibleActions = this.rowActions.filter((action) => action.visible(row))
      return visibleActions.slice(2)
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },

    // 处理页面大小变化
    handleSizeChange(size) {
      this.$emit('pagination-change', { page: this.currentPage, size })
    },

    // 处理当前页变化
    handleCurrentChange(page) {
      this.$emit('pagination-change', { page, size: this.pageSize })
    },

    // 移除异常
    async handleRemoveException(row) {
      try {
        await ElMessageBox.confirm(
          `移除异常后，该出库单的状态会变更为【待处理】，需要重新生成波次才能出库，确认继续吗?`,
          '移除异常',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          },
        )

        const params = {
          list: [
            {
              deliveryNo: row.deliveryNo,
              customerCode: row.customerCode,
            },
          ],
          whCode: row.whCode,
        }

        const response = await removeEx(params)

        if (response && response.code === 200) {
          ElMessage.success('移除异常成功')
          this.$emit('refresh')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('移除异常失败', error)
          ElMessage.error('移除异常失败')
        }
      }
    },

    // 确认异常
    async handleEnterException(row) {
      try {
        const { value: exceptionRemark } = await ElMessageBox.prompt('请填写异常原因', '确认异常', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputType: 'textarea',
          inputPlaceholder: '请输入异常原因...',
          inputValidator: (value) => {
            if (!value || value.trim() === '') {
              return '异常原因不能为空'
            }
            return true
          },
        })

        const params = {
          customerCode: row.customerCode,
          deliveryNo: row.deliveryNo,
          whCode: row.whCode,
          exceptionRemark: exceptionRemark.trim(),
        }

        const response = await confirmEx(params)

        if (response && response.code === 200) {
          ElMessage.success('确认异常成功')
          this.$emit('refresh')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('确认异常失败', error)
          ElMessage.error('确认异常失败')
        }
      }
    },

    // 处理行操作
    handleRowAction(action, row) {
      switch (action.key) {
        case 'remove_exception':
          this.handleRemoveException(row)
          break
        case 'enter_exception':
          this.handleEnterException(row)
          break
        default:
          console.warn(`未知的操作: ${action.key}`)
      }
    },
  },
}
</script>

<style scoped>
.table-card {
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 20px 20px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

.copy-icon {
  margin-left: 8px;
  cursor: pointer;
  color: var(--el-color-primary);
  font-size: 14px;
}

.copy-icon:hover {
  color: var(--el-color-primary-light-3);
}
</style>
