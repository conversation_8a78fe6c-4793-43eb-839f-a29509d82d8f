<template>
  <div class="abnormal-container">
    <!-- 搜索区域 -->
    <AbnormalSearch
      :queryParams="queryParams"
      :customerOptions="customerOptions"
      :logisticsChannelOptions="logisticsChannelOptions"
      :logisticsCarrierOptions="logisticsCarrierOptions"
      @search="handleQuery"
      @reset="resetQuery"
    />

    <!-- 批量操作区域 -->
    <AbnormalBatchActions
      :selectedRows="selectedRows"
      @remove_exception="handleRemoveException"
      @check_export="handleCheckExport"
      @filter_export="handleFilterExport"
    />

    <!-- 表格区域 -->
    <AbnormalTable
      :loading="loading"
      :abnormalList="abnormalList"
      :total="total"
      :currentPage="currentPage"
      :pageSize="pageSize"
      @selection-change="handleSelectionChange"
      @pagination-change="handlePaginationChange"
      @row-action="handleRowAction"
      @refresh="fetchAbnormalList"
    />
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAbnormalList, batchProcessAbnormal, exportAbnormalData } from '@/api/outbound/abnormal'
import AbnormalSearch from './components/AbnormalSearch.vue'
import AbnormalBatchActions from './components/AbnormalBatchActions.vue'
import AbnormalTable from './components/AbnormalTable.vue'
import { channels, consumers, options } from '@/api/outbound/parcel'

export default {
  name: 'AbnormalIndex',
  components: {
    AbnormalSearch,
    AbnormalBatchActions,
    AbnormalTable,
  },
  data() {
    return {
      // 加载状态
      loading: false,

      // 查询参数
      queryParams: {
        // 分页参数
        current: 1,
        size: 20,

        // 基础筛选参数
        whCode: 'CA', // 默认仓库，不在界面显示
        customerCodes: [],
        logisticsChannel: [],
        logisticsCarrier: [],
        outboundType: '',

        // 时间参数
        timeType: 'exceptionTime',
        timeRange: this.getDefaultTimeRange(),
        startTime: '',
        endTime: '',

        // 搜索参数
        searchType: 'barcode',
        multiKeyword: '',
        barcode: '',
        platformOrderNo: '',
        waveNo: '',
        sourceNo: '',
        expressNo: '',
      },

      // 表格数据
      abnormalList: [],
      total: 0,
      currentPage: 1,
      pageSize: 20,

      // 表格选中行
      selectedRows: [],

      // 下拉选项数据
      customerOptions: [],
      logisticsChannelOptions: [],
      logisticsCarrierOptions: [],
    }
  },
  async mounted() {
    this.loading = true

    try {
      // 并行获取基础数据
      await Promise.all([
        this.fetchCustomerOptions(),
        this.fetchLogisticsChannelOptions(),
        this.fetchLogisticsCarrierOptions(),
      ])

      // 获取列表数据
      await this.fetchAbnormalList(false)
    } catch (error) {
      console.error('初始化数据失败', error)
      ElMessage.error('初始化数据失败')
    } finally {
      this.loading = false
    }
  },
  methods: {
    // 生成默认时间范围（最近3个月）
    getDefaultTimeRange() {
      const today = new Date()
      const end = new Date(today)
      end.setDate(end.getDate() + 1)
      const start = new Date(today)
      start.setMonth(start.getMonth() - 3)
      return {
        type: 'exceptionTime',
        value: [start, end],
      }
    },

    // 格式化日期为 Y-m-d HH:mm:ss 格式
    formatDateTime(date, isEndTime = false) {
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const time = isEndTime ? '23:59:59' : '00:00:00'
      return `${year}-${month}-${day} ${time}`
    },

    // 构建查询参数
    buildQueryParams() {
      const params = { ...this.queryParams }

      // 处理时间范围参数
      if (params.timeRange && params.timeRange.type && params.timeRange.value) {
        params.timeType = params.timeRange.type
        if (Array.isArray(params.timeRange.value) && params.timeRange.value.length === 2) {
          // 如果是Date对象，转换为字符串格式
          if (params.timeRange.value[0] instanceof Date) {
            params.startTime = this.formatDateTime(params.timeRange.value[0], false)
            params.endTime = this.formatDateTime(params.timeRange.value[1], true)
          } else {
            params.startTime = params.timeRange.value[0]
            params.endTime = params.timeRange.value[1]
          }
        }
      }

      // 处理搜索参数
      const searchTypeFields = ['barcode', 'platformOrderNo', 'waveNo', 'sourceNo', 'expressNo']

      if (params.searchType && params.multiKeyword) {
        params[params.searchType] = params.multiKeyword
      }

      // 清空 multiKeyword 但保留字段传递
      params.multiKeyword = ''

      // 删除没有值的搜索类型字段，只传递有值的字段
      searchTypeFields.forEach(field => {
        if (!params[field] || params[field].trim() === '') {
          delete params[field]
        }
      })

      // 处理多选参数 - 转换为逗号分隔的字符串
      if (Array.isArray(params.customerCodes) && params.customerCodes.length > 0) {
        params.customerCodes = params.customerCodes.join(',')
      } else {
        params.customerCodes = ''
      }

      if (Array.isArray(params.logisticsChannel) && params.logisticsChannel.length > 0) {
        params.logisticsChannel = params.logisticsChannel.join(',')
      } else {
        params.logisticsChannel = ''
      }

      if (Array.isArray(params.logisticsCarrier) && params.logisticsCarrier.length > 0) {
        params.logisticsCarrier = params.logisticsCarrier.join(',')
      } else {
        params.logisticsCarrier = ''
      }

      // 删除不需要传递给后端的参数
      delete params.timeRange

      return params
    },

    // 获取异常件列表
    async fetchAbnormalList(showLoading = true) {
      if (showLoading) {
        this.loading = true
      }

      try {
        const params = this.buildQueryParams()
        console.log('查询参数:', params)

        const response = await getAbnormalList(params)

        if (response && response.code === 200) {
          // 处理嵌套的数据结构：response.data.data.records
          const dataObj = response.data.data || response.data
          this.abnormalList = dataObj.records || dataObj.list || []
          this.total = dataObj.total || 0
          this.currentPage = dataObj.current || 1
          this.pageSize = dataObj.size || 20
        }
      } catch (error) {
        console.error('获取异常件列表失败', error)
        ElMessage.error('获取异常件列表失败')
      } finally {
        if (showLoading) {
          this.loading = false
        }
      }
    },

    // 获取客户选项
    async fetchCustomerOptions() {
      try {
        const response = await consumers()
        if (response && response.code === 200) {
          this.customerOptions = (response.data || []).map((item) => ({
            value: item.customerCode,
            label: item.customerName,
          }))
        }
      } catch (error) {
        console.error('获取客户列表失败', error)
      }
    },

    // 获取物流渠道选项
    async fetchLogisticsChannelOptions() {
      try {
        const response = await channels()
        if (response && response.code === 200) {
          this.logisticsChannelOptions = (response.data || []).map((item) => ({
            value: item.logisticsChannel,
            label: item.logisticsChannelName,
          }))
        }
      } catch (error) {
        console.error('获取物流渠道列表失败', error)
      }
    },

    // 获取承运商选项
    async fetchLogisticsCarrierOptions() {
      try {
        const response = await options()
        if (response && response.code === 200) {
          this.logisticsCarrierOptions = (response.data.carrierList || []).map((item) => ({
            value: item.value,
            label: item.en,
          }))
        }
      } catch (error) {
        console.error('获取承运商列表失败', error)
      }
    },

    // 搜索
    handleQuery() {
      this.currentPage = 1
      this.queryParams.current = 1
      this.fetchAbnormalList()
    },

    // 重置搜索
    resetQuery() {
      this.queryParams = {
        current: 1,
        size: 20,
        whCode: 'CA', // 默认仓库，不在界面显示
        outboundType: '',
        customerCodes: [],
        logisticsChannel: [],
        logisticsCarrier: [],
        outboundType: '',
        timeType: 'exceptionTime',
        timeRange: this.getDefaultTimeRange(),
        startTime: '',
        endTime: '',
        searchType: 'barcode',
        multiKeyword: '',
        barcode: '',
        platformOrderNo: '',
        waveNo: '',
        sourceNo: '',
        expressNo: '',
      }
      this.handleQuery()
    },

    // 处理分页变化
    handlePaginationChange({ page, size }) {
      this.currentPage = page
      this.pageSize = size
      this.queryParams.current = page
      this.queryParams.size = size
      this.fetchAbnormalList()
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 处理行操作
    handleRowAction({ action, row }) {
      switch (action) {
        case 'view':
          this.handleViewDetail(row)
          break
        case 'process':
          this.handleProcessSingle(row)
          break
        case 'edit':
          this.handleEditSingle(row)
          break
        case 'delete':
          this.handleDeleteSingle(row)
          break
        default:
          console.warn(`未知的行操作: ${action}`)
      }
    },

    // 查看详情
    handleViewDetail(row) {
      this.$router.push({
        path: '/outbound/abnormal/detail',
        query: { id: row.id },
      })
    },

    // 单个处理
    handleProcessSingle(row) {
      ElMessage.info(`处理异常件: ${row.barcode}`)
    },

    // 单个编辑
    handleEditSingle(row) {
      ElMessage.info(`编辑异常件: ${row.barcode}`)
    },

    // 单个删除
    async handleDeleteSingle(row) {
      try {
        await ElMessageBox.confirm(`确认删除异常件：${row.barcode}？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        ElMessage.success(`删除成功：${row.barcode}`)
        this.fetchAbnormalList(false)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败', error)
          ElMessage.error('删除失败')
        }
      }
    },

    // 移除异常
    async handleRemoveException(data) {
      try {
        this.loading = true
        const response = await batchProcessAbnormal(data)

        if (response && response.code === 200) {
          ElMessage.success('批量处理成功')
          this.fetchAbnormalList(false)
        }
      } catch (error) {
        console.error('批量处理失败', error)
        ElMessage.error('批量处理失败')
      } finally {
        this.loading = false
      }
    },

    // 按勾选数据导出
    async handleCheckExport(data) {
      try {
        // 确认导出操作
        await ElMessageBox.confirm(`确认导出选中的 ${data.ids.length} 条异常件数据？`, '导出确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        })

        this.loading = true

        // 调用导出API
        const response = await exportAbnormalData(data)

        if (response && response.code === 200) {
          ElMessage.success(`成功导出 ${data.ids.length} 条记录`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('按勾选数据导出失败', error)
          ElMessage.error('导出失败')
        }
      } finally {
        this.loading = false
      }
    },

    // 按筛选数据导出
    async handleFilterExport() {
      try {
        // 获取当前筛选条件
        const filterParams = this.buildQueryParams()

        // 确认导出操作
        await ElMessageBox.confirm('确认按当前筛选条件导出异常件数据？', '导出确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        })

        this.loading = true

        // 调用导出API
        const response = await exportAbnormalData(filterParams)

        if (response && response.code === 200) {
          ElMessage.success('导出成功')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('按筛选数据导出失败', error)
          ElMessage.error('导出失败')
        }
      } finally {
        this.loading = false
      }
    },
  },
}
</script>

<style scoped>
.abnormal-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 120px);
}
</style>
