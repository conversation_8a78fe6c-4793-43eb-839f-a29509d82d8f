<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { secondSortScan, scanQuery, getPrinter, setPrinter } from '@/api/outbound/secondSort'
import ScanArea from './components/ScanArea.vue'
import OrderList from './components/OrderList.vue'
import SettingDialog from './components/SettingDialog.vue'
import PrintDialog from './components/PrintDialog.vue'

// 路由
const route = useRoute()

// 播种墙数据
const seedWallData = ref(null)
const orderNoList = ref([])

// 初始波次号（从路由参数获取）
const initialWaveNo = ref('')

// 表格加载状态
const loading = ref(false)
const barcodeDisabled = ref(true)
const orderNoDisabled = ref(false)

const printData = ref([])

const settingDialog = reactive({
  visible: false,
  title: '二次分拣设置',
})

const printDialog = reactive({
  visible: false,
  title: '',
})

const settingData = ref({
  seedWallType: '', // 播种墙  1  自定义   2   系统播种墙
  seedWallMapNum: 1, // 自定义值 (数字类型)
  seedWallMapType: '', // 自定义类型  row 行 column 列
  print_shipping_list_enabled: '', // UI 是否启用打印发货清单 1 启用 0 未启用
  print_shipping_list: '', // 打印发货清单 1 分拣时开始打印  2 每单分拣完成时打印
  print_express_enabled: '', // UI 是否启用打印物流面单 1 启用 0 未启用
  print_express: '', // 打印物流面单 1 分拣开始时打印  2 每单分拣完成时打印
  isPrintRepeatTip: '', // 重复打印物流面单时提醒  1 勾选  0 未选
  isScanPack: '', // 扫描包材 1 勾选  0 未选
  isAutoWeight: '', // 二次分拣后立即自动计算重量 1 勾选  0 未选
  quickOutbound: '', // 快捷出库 1 勾选  0 未选
})

// 获取订单列表
const getList = async (orderNo) => {
  loading.value = true

  try {
    if (orderNoList.value.includes(orderNo)) {
      return
    }

    const params = {
      noBuildSKuInfo: true,
      waveNo: orderNo,
      whCode: 'CA',
    }

    // 只传递勾选或选中的settingData字段
    if (settingData.value.seedWallType && settingData.value.seedWallType !== '') {
      params.seedWallType = settingData.value.seedWallType
      // 如果选择了播种墙类型，同时传递相关配置
      if (
        settingData.value.seedWallMapNum &&
        settingData.value.seedWallMapNum !== '' &&
        settingData.value.seedWallMapNum !== 0
      ) {
        params.seedWallMapNum = String(settingData.value.seedWallMapNum) // 转换为字符串
      }
      if (settingData.value.seedWallMapType && settingData.value.seedWallMapType !== '') {
        params.seedWallMapType = settingData.value.seedWallMapType
      }
    }

    // 打印发货清单：只有启用且选择了时机才传递
    if (
      settingData.value.print_shipping_list_enabled === '1' &&
      settingData.value.print_shipping_list &&
      settingData.value.print_shipping_list !== ''
    ) {
      params.print_shipping_list = settingData.value.print_shipping_list
    }

    // 打印物流面单：只有启用且选择了时机才传递
    if (
      settingData.value.print_express_enabled === '1' &&
      settingData.value.print_express &&
      settingData.value.print_express !== ''
    ) {
      params.print_express = settingData.value.print_express
    }

    // 其他勾选项：只有勾选（值为'1'）才传递
    if (settingData.value.isPrintRepeatTip === '1') {
      params.isPrintRepeatTip = settingData.value.isPrintRepeatTip
    }
    if (settingData.value.isScanPack === '1') {
      params.isScanPack = settingData.value.isScanPack
    }
    if (settingData.value.isAutoWeight === '1') {
      params.isAutoWeight = settingData.value.isAutoWeight
    }
    if (settingData.value.quickOutbound === '1') {
      params.quickOutbound = settingData.value.quickOutbound
    }

    console.log(params)

    // 先调用 scanQuery 接口进行校验
    const queryRes = await scanQuery({ orderNo: orderNo, whCode: 'CA' })
    if (queryRes.code !== 200) {
      seedWallData.value = null
      ElMessage.error(queryRes.message || '校验失败')
      return
    }

    // 校验通过后，调用 secondSortScan 接口
    const res = await secondSortScan(params)
    if (res.code === 200 && res.data) {
      orderNoList.value.push(orderNo)
      seedWallData.value = res.data
      barcodeDisabled.value = false
      orderNoDisabled.value = true

      // 检查是否需要显示重复打印提醒
      if (
        settingData.value.isPrintRepeatTip == '1' &&
        res.data.seedWallDTO?.gridList?.length > 0 &&
        res.data.seedWallDTO.gridList[0].expressPrintStatus == '20'
      ) {
        const printRow = {
          sourceNo: orderNo,
          printStatusName: '已打印',
        }
        printData.value.push(printRow)

        printDialog.visible = true
      }
    } else {
      seedWallData.value = null
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    loading.value = false
  }
}

const handleScan = (orderNo) => {
  getList(orderNo)
}

const handleReset = () => {
  // 重置所有状态
  seedWallData.value = null
  orderNoList.value = []
  barcodeDisabled.value = true
  orderNoDisabled.value = false
}

const handleSetting = () => {
  settingDialog.visible = true
}



const settingCancel = () => {
  settingDialog.visible = false
}

const settingSuccess = () => {
  settingDialog.visible = false

  setUserPrinter()
}

const printCancel = () => {
  printData.value = []

  printDialog.visible = false
}

const printSuccess = () => {
  printData.value = []
  printDialog.visible = false
}

//获取用户打印设置
const getUserPrinter = async () => {
  try {
    const res = await getPrinter()
    if (res.code === 200 && res.data) {
      // 定义默认值配置（不包含UI控制字段）
      const defaultSettings = {
        seedWallType: '1', // 默认自定义
        seedWallMapNum: 1, // 默认值为1 (数字类型)
        seedWallMapType: 'row', // 默认行
        print_shipping_list: '', // 默认空值
        print_express: '', // 默认空值
        isPrintRepeatTip: '0', // 默认未选中
        isScanPack: '0', // 默认未选中
        isAutoWeight: '0', // 默认未选中
        quickOutbound: '0', // 默认未选中
      }

      // 合并后端数据和默认值，后端数据优先
      Object.keys(defaultSettings).forEach((key) => {
        // 只有当后端数据为 undefined、null 或空字符串时才使用默认值
        const backendValue = res.data[key]
        if (backendValue !== undefined && backendValue !== null && backendValue !== '') {
          // seedWallMapNum 需要转换为数字类型，其他保持字符串
          if (key === 'seedWallMapNum') {
            settingData.value[key] = Number(backendValue)
          } else {
            settingData.value[key] = String(backendValue)
          }
        } else {
          // 默认值中 seedWallMapNum 也需要转换为数字
          if (key === 'seedWallMapNum') {
            settingData.value[key] = Number(defaultSettings[key])
          } else {
            settingData.value[key] = defaultSettings[key]
          }
        }
      })

      // 根据实际值设置UI控制字段
      // print_shipping_list_enabled: 如果 print_shipping_list 有有效值(1或2)则启用，否则不启用
      settingData.value.print_shipping_list_enabled =
        settingData.value.print_shipping_list === '1' ||
        settingData.value.print_shipping_list === '2'
          ? '1'
          : '0'

      // print_express_enabled: 如果 print_express 有有效值(1或2)则启用，否则不启用
      settingData.value.print_express_enabled =
        settingData.value.print_express === '1' || settingData.value.print_express === '2'
          ? '1'
          : '0'
    } else {
      // 如果接口调用失败，设置所有默认值
      settingData.value = {
        seedWallType: '1',
        seedWallMapNum: 1, // 数字类型
        seedWallMapType: 'row',
        print_shipping_list_enabled: '0', // UI控制字段，默认不启用
        print_shipping_list: '',
        print_express_enabled: '0', // UI控制字段，默认不启用
        print_express: '',
        isPrintRepeatTip: '0',
        isScanPack: '0',
        isAutoWeight: '0',
        quickOutbound: '0',
      }
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
    // 异常情况下也设置默认值
    settingData.value = {
      seedWallType: '1',
      seedWallMapNum: 1, // 数字类型
      seedWallMapType: 'row',
      print_shipping_list_enabled: '0', // UI控制字段，默认不启用
      print_shipping_list: '',
      print_express_enabled: '0', // UI控制字段，默认不启用
      print_express: '',
      isPrintRepeatTip: '0',
      isScanPack: '0',
      isAutoWeight: '0',
      quickOutbound: '0',
    }
  }
}

//用户打印设置
const setUserPrinter = async () => {
  try {
    const params = {
      seedWallType: settingData.value.seedWallType,
      seedWallMapNum: String(settingData.value.seedWallMapNum), // 转换为字符串发送给后端
      seedWallMapType: settingData.value.seedWallMapType,
      // 注意：不保存 print_shipping_list_enabled 和 print_express_enabled，这两个字段仅用于UI控制
      print_shipping_list: settingData.value.print_shipping_list,
      print_express: settingData.value.print_express,
      isPrintRepeatTip: settingData.value.isPrintRepeatTip,
      isScanPack: settingData.value.isScanPack,
      isAutoWeight: settingData.value.isAutoWeight,
      quickOutbound: settingData.value.quickOutbound,
    }
    console.log('保存配置参数:', params)
    const res = await setPrinter(params)
    if (res.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(res.message || '配置保存失败')
    }
  } catch (error) {
    console.error('保存配置失败', error)
    ElMessage.error('配置保存失败')
  }
}

// 初始化
onMounted(() => {
  // 检查路由参数中是否有波次号
  if (route.query.waveNo) {
    initialWaveNo.value = route.query.waveNo
  }

  getUserPrinter()
})
</script>

<template>
  <div class="swap-container">
    <el-row :gutter="20">
      <!-- 左侧扫描区域 -->
      <el-col :span="6">
        <ScanArea
          :loading="loading"
          :barcodeDisabled="barcodeDisabled"
          :orderNoDisabled="orderNoDisabled"
          :initialWaveNo="initialWaveNo"
          @scan="handleScan"
          @setting="handleSetting"
          @reset="handleReset"
        />
      </el-col>

      <!-- 右侧订单区域 -->
      <el-col :span="18">
        <OrderList
          :loading="loading"
          :seedWallData="seedWallData"
          :settingData="settingData"
        />
      </el-col>
    </el-row>

    <!-- Setting -->
    <SettingDialog
      :visible="settingDialog.visible"
      :title="settingDialog.title"
      :settingData="settingData"
      @cancel="settingCancel"
      @success="settingSuccess"
    />

    <!-- 打印 -->
    <PrintDialog
      :visible="printDialog.visible"
      :printData="printData"
      @cancel="printCancel"
      @success="printSuccess"
    />
  </div>
</template>

<style scoped>
.swap-container {
  padding: 20px;
  min-height: calc(100vh - 120px);
}
</style>
